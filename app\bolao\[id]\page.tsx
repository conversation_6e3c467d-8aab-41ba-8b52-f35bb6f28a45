"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Trophy,
  Users,
  DollarSign,
  Calendar,
  Clock,
  ArrowLeft,
  Target,
  CheckCircle,
  AlertCircle,
  CreditCard,
  Smartphone,
} from "lucide-react"
import Link from "next/link"
import CompetitionEmblem from "@/components/CompetitionEmblem"
import { useRouter, useParams } from "next/navigation"

interface Jogo {
  id: number
  time_casa: string
  time_fora: string
  data_jogo: string
  campeonato: string
  resultado_casa?: number
  resultado_fora?: number
  status: "agendado" | "ao_vivo" | "finalizado"
}

interface Bolao {
  id: number
  nome: string
  descricao: string
  valor_aposta: number
  premio_total: number
  data_inicio: string
  data_fim: string
  status: "ativo" | "encerrado" | "em_breve"
  participantes: number
  max_participantes: number
  campeonatos: string[]
  regras: string[]
  jogos: Jogo[]
}

interface ApostaJogo {
  jogo_id: number
  resultado: "casa" | "empate" | "fora" | null
}

export default function BolaoPage() {
  const [bolao, setBolao] = useState<Bolao | null>(null)
  const [apostas, setApostas] = useState<ApostaJogo[]>([])
  const [loading, setLoading] = useState(true)
  const [finalizando, setFinalizando] = useState(false)
  const [showPayment, setShowPayment] = useState(false)
  const router = useRouter()
  const params = useParams()

  useEffect(() => {
    loadBolaoData()
  }, [params.id])

  const loadBolaoData = async () => {
    try {
      setLoading(true)

      // Buscar dados reais do bolão
      const response = await fetch(`/api/boloes/${params.id}`)
      if (!response.ok) {
        throw new Error('Bolão não encontrado')
      }

      const bolaoData = await response.json()
      setBolao(bolaoData)

      // Inicializar apostas vazias
      const apostasIniciais = bolaoData.jogos?.map((jogo: Jogo) => ({
        jogo_id: jogo.id,
        resultado: null as "casa" | "empate" | "fora" | null,
      })) || []
      setApostas(apostasIniciais)
    } catch (error) {
      console.error("Erro ao carregar bolão:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleApostaChange = (jogoId: number, resultado: "casa" | "empate" | "fora") => {
    setApostas((prev) => prev.map((aposta) => (aposta.jogo_id === jogoId ? { ...aposta, resultado } : aposta)))
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getApostasFeitas = () => {
    return apostas.filter((aposta) => aposta.resultado !== null).length
  }

  const podeFinalizarAposta = () => {
    return getApostasFeitas() >= 3 // Mínimo 3 apostas
  }

  const handleFinalizarAposta = () => {
    if (!podeFinalizarAposta()) {
      return
    }
    setShowPayment(true)
  }

  const handleConfirmarPagamento = async () => {
    setFinalizando(true)

    try {
      // Simular processamento do pagamento
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // Redirecionar para página de sucesso ou dashboard
      router.push("/dashboard?success=aposta_realizada")
    } catch (error) {
      console.error("Erro ao processar pagamento:", error)
    } finally {
      setFinalizando(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando bolão...</p>
        </div>
      </div>
    )
  }

  if (!bolao) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Bolão não encontrado</h2>
          <p className="text-gray-600 mb-4">O bolão que você está procurando não existe ou foi removido.</p>
          <Link href="/">
            <Button>Voltar ao Início</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (showPayment) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            <div className="flex justify-between items-center h-14 sm:h-16">
              <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
                <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
                <span className="text-lg sm:text-2xl font-bold text-gray-900">Sistema Bolão</span>
              </Link>
            </div>
          </div>
        </header>

        <div className="max-w-2xl mx-auto px-4 py-8">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Finalizar Aposta</CardTitle>
              <CardDescription>Confirme seus dados e realize o pagamento</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Resumo da Aposta */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3">Resumo da Aposta</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Bolão:</span>
                    <span className="font-medium">{bolao.nome}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Jogos apostados:</span>
                    <span className="font-medium">
                      {getApostasFeitas()} de {bolao.jogos.length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Valor da aposta:</span>
                    <span className="font-medium text-green-600">{formatCurrency(bolao.valor_aposta)}</span>
                  </div>
                </div>
              </div>

              {/* Suas Apostas */}
              <div>
                <h3 className="font-semibold mb-3">Suas Apostas</h3>
                <div className="space-y-2">
                  {apostas
                    .filter((a) => a.resultado)
                    .map((aposta) => {
                      const jogo = bolao.jogos.find((j) => j.id === aposta.jogo_id)
                      if (!jogo) return null

                      return (
                        <div
                          key={aposta.jogo_id}
                          className="flex justify-between items-center text-sm p-2 bg-white rounded border"
                        >
                          <span>
                            {jogo.time_casa} vs {jogo.time_fora}
                          </span>
                          <Badge variant="outline">
                            {aposta.resultado === "casa"
                              ? "Vitória Casa"
                              : aposta.resultado === "empate"
                                ? "Empate"
                                : "Vitória Fora"}
                          </Badge>
                        </div>
                      )
                    })}
                </div>
              </div>

              {/* Pagamento PIX */}
              <div className="border-t pt-6">
                <h3 className="font-semibold mb-4 flex items-center">
                  <Smartphone className="h-5 w-5 mr-2" />
                  Pagamento via PIX
                </h3>

                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="bg-white p-4 rounded-lg inline-block mb-4">
                    {/* QR Code simulado */}
                    <div className="w-48 h-48 bg-gray-200 flex items-center justify-center rounded">
                      <div className="text-center">
                        <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">QR Code PIX</p>
                        <p className="text-xs text-gray-500">{formatCurrency(bolao.valor_aposta)}</p>
                      </div>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4">
                    Escaneie o QR Code com seu app do banco ou copie a chave PIX
                  </p>

                  <div className="bg-white p-3 rounded border text-xs font-mono break-all mb-4">
                    00020126580014BR.GOV.BCB.PIX013636297073-0001-0000-0000-000000000000520400005303986540525.005802BR5925Sistema
                    Bolao LTDA6009SAO PAULO62070503***6304ABCD
                  </div>

                  <Button variant="outline" className="mb-4">
                    Copiar Chave PIX
                  </Button>
                </div>

                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Após realizar o pagamento, clique em "Confirmar Pagamento" para finalizar sua aposta. O sistema
                    verificará automaticamente o recebimento.
                  </AlertDescription>
                </Alert>
              </div>

              {/* Botões */}
              <div className="flex space-x-4 pt-6">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowPayment(false)}
                  disabled={finalizando}
                >
                  Voltar
                </Button>
                <Button
                  className="flex-1 bg-green-600 hover:bg-green-700"
                  onClick={handleConfirmarPagamento}
                  disabled={finalizando}
                >
                  {finalizando ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processando...</span>
                    </div>
                  ) : (
                    "Confirmar Pagamento"
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14 sm:h-16">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
              <span className="text-lg sm:text-2xl font-bold text-gray-900">Sistema Bolão</span>
            </Link>
            <Link href="/">
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Voltar</span>
                <span className="sm:hidden">←</span>
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Cabeçalho do Bolão */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{bolao.nome}</h1>
              <p className="text-gray-600">{bolao.descricao}</p>
            </div>
            <Badge variant={bolao.status === "ativo" ? "default" : "secondary"} className="text-lg px-4 py-2">
              {bolao.status === "ativo" ? "Ativo" : "Encerrado"}
            </Badge>
          </div>

          {/* Informações do Bolão */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-4 text-center">
                <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Valor da Aposta</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(bolao.valor_aposta)}</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Trophy className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Prêmio Total</p>
                <p className="text-2xl font-bold text-orange-600">{formatCurrency(bolao.premio_total)}</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Participantes</p>
                <p className="text-2xl font-bold text-blue-600">
                  {bolao.participantes}/{bolao.max_participantes}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Encerra em</p>
                <p className="text-lg font-bold text-purple-600">{formatDateTime(bolao.data_fim)}</p>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Jogos para Apostar */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Faça suas Apostas
                </CardTitle>
                <CardDescription>Selecione o resultado de cada jogo. Mínimo 3 apostas para participar.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bolao.jogos.map((jogo) => {
                    const apostaAtual = apostas.find((a) => a.jogo_id === jogo.id)

                    return (
                      <div key={jogo.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{jogo.campeonato}</Badge>
                            <span className="text-sm text-gray-600 flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {formatDateTime(jogo.data_jogo)}
                            </span>
                          </div>
                          {apostaAtual?.resultado && <CheckCircle className="h-5 w-5 text-green-600" />}
                        </div>

                        <div className="text-center mb-4">
                          <div className="flex items-center justify-between">
                            <div className="text-center flex-1">
                              <div className="font-semibold text-lg">{jogo.time_casa}</div>
                            </div>
                            <div className="mx-4">
                              <div className="text-2xl font-bold text-gray-400">VS</div>
                            </div>
                            <div className="text-center flex-1">
                              <div className="font-semibold text-lg">{jogo.time_fora}</div>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-3 gap-2">
                          <Button
                            variant={apostaAtual?.resultado === "casa" ? "default" : "outline"}
                            className={`w-full transition-all duration-200 ${
                              apostaAtual?.resultado === "casa"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white"
                            }`}
                            onClick={() => handleApostaChange(jogo.id, "casa")}
                          >
                            Casa
                          </Button>
                          <Button
                            variant={apostaAtual?.resultado === "empate" ? "default" : "outline"}
                            className={`w-full transition-all duration-200 ${
                              apostaAtual?.resultado === "empate"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white"
                            }`}
                            onClick={() => handleApostaChange(jogo.id, "empate")}
                          >
                            Empate
                          </Button>
                          <Button
                            variant={apostaAtual?.resultado === "fora" ? "default" : "outline"}
                            className={`w-full transition-all duration-200 ${
                              apostaAtual?.resultado === "fora"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white"
                            }`}
                            onClick={() => handleApostaChange(jogo.id, "fora")}
                          >
                            Fora
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Progresso */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Seu Progresso</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Apostas feitas</span>
                      <span>
                        {getApostasFeitas()}/{bolao.jogos.length}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full transition-all"
                        style={{ width: `${(getApostasFeitas() / bolao.jogos.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600 mb-1">{formatCurrency(bolao.valor_aposta)}</p>
                    <p className="text-sm text-gray-600">Valor total da aposta</p>
                  </div>

                  {!podeFinalizarAposta() && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Você precisa fazer pelo menos 3 apostas para participar do bolão.
                      </AlertDescription>
                    </Alert>
                  )}

                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    disabled={!podeFinalizarAposta()}
                    onClick={handleFinalizarAposta}
                  >
                    {podeFinalizarAposta() ? "Finalizar Aposta" : `Faltam ${3 - getApostasFeitas()} apostas`}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Regras */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Regras do Bolão</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  {bolao.regras.map((regra, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span>{regra}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Campeonatos */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Campeonatos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-3">
                  {bolao.campeonatos.map((campeonato, index) => (
                    <div key={index} className="flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                      <CompetitionEmblem
                        alt={campeonato}
                        competitionCode={campeonato.toLowerCase().replace(/\s+/g, '-')}
                        size="sm"
                      />
                      <span className="text-sm font-medium text-blue-800">
                        {campeonato}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
