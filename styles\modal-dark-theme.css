/* TEMA ESCURO PERSONALIZADO PARA MODAIS - FUNDO #1E293B */

/* Força o fundo #1E293B em todos os modais */
[data-radix-dialog-content] {
  background-color: #1E293B !important;
  color: white !important;
  border: 1px solid #334155 !important;
}

/* Estilos para textos dentro dos modais */
[data-radix-dialog-content] h1,
[data-radix-dialog-content] h2,
[data-radix-dialog-content] h3,
[data-radix-dialog-content] h4,
[data-radix-dialog-content] h5,
[data-radix-dialog-content] h6 {
  color: white !important;
}

[data-radix-dialog-content] p,
[data-radix-dialog-content] span,
[data-radix-dialog-content] div,
[data-radix-dialog-content] label {
  color: white !important;
}

/* Estilos para cards e containers */
[data-radix-dialog-content] .card,
[data-radix-dialog-content] [class*="card"],
[data-radix-dialog-content] .bg-white,
[data-radix-dialog-content] .bg-gray-50,
[data-radix-dialog-content] .bg-gray-100 {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

/* Estilos para badges e status */
[data-radix-dialog-content] .badge,
[data-radix-dialog-content] [class*="badge"] {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

/* Estilos para botões */
[data-radix-dialog-content] button {
  color: white !important;
}

[data-radix-dialog-content] button:not(.bg-green-600):not(.bg-blue-600):not(.bg-red-600) {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

/* Estilos para inputs */
[data-radix-dialog-content] input,
[data-radix-dialog-content] textarea,
[data-radix-dialog-content] select {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

[data-radix-dialog-content] input::placeholder,
[data-radix-dialog-content] textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Estilos para separadores */
[data-radix-dialog-content] hr,
[data-radix-dialog-content] .border-t,
[data-radix-dialog-content] .border-b {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Estilos específicos para listas de bilhetes e pagamentos */
[data-radix-dialog-content] .payment-item,
[data-radix-dialog-content] .ticket-item,
[data-radix-dialog-content] [class*="border-l-"] {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Estilos para ícones */
[data-radix-dialog-content] svg {
  color: white !important;
}

/* Estilos para textos com cores específicas */
[data-radix-dialog-content] .text-gray-500,
[data-radix-dialog-content] .text-gray-600,
[data-radix-dialog-content] .text-gray-700,
[data-radix-dialog-content] .text-gray-800,
[data-radix-dialog-content] .text-gray-900 {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Manter cores de status importantes */
[data-radix-dialog-content] .text-green-500,
[data-radix-dialog-content] .text-green-600 {
  color: rgb(34 197 94) !important;
}

[data-radix-dialog-content] .text-yellow-500,
[data-radix-dialog-content] .text-yellow-600 {
  color: rgb(251 191 36) !important;
}

[data-radix-dialog-content] .text-red-500,
[data-radix-dialog-content] .text-red-600 {
  color: rgb(239 68 68) !important;
}

/* Estilos para QR Code container */
[data-radix-dialog-content] .qr-code-container,
[data-radix-dialog-content] [class*="qr"] {
  background-color: white !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
}

/* Responsividade específica para mobile */
@media (max-width: 640px) {
  [data-radix-dialog-content] {
    background-color: #1E293B !important;
    color: white !important;
    padding: 1rem !important;
    margin: 0.5rem !important;
    border-radius: 1rem !important;
  }
}

/* Responsividade para telas muito pequenas */
@media (max-width: 375px) {
  [data-radix-dialog-content] {
    width: 98vw !important;
    max-width: 98vw !important;
    padding: 0.75rem !important;
    margin: 0.25rem !important;
  }
}

/* Estilos para iOS */
@supports (-webkit-touch-callout: none) {
  [data-radix-dialog-content] {
    -webkit-overflow-scrolling: touch !important;
    background-color: #1E293B !important;
  }
}

/* Estilos para Android */
@media screen and (max-device-width: 640px) {
  [data-radix-dialog-content] {
    background-color: #1E293B !important;
    color: white !important;
  }
}

/* Estilos para dispositivos com notch */
@media screen and (max-width: 640px) and (min-height: 812px) {
  [data-radix-dialog-content] {
    margin-top: env(safe-area-inset-top, 1rem) !important;
    margin-bottom: env(safe-area-inset-bottom, 1rem) !important;
  }
}

/* Estilos para landscape em mobile */
@media screen and (max-height: 640px) and (orientation: landscape) {
  [data-radix-dialog-content] {
    max-height: 90vh !important;
    width: 85vw !important;
    max-width: 600px !important;
  }
}

/* Garantir que o scrollbar seja visível no tema escuro */
[data-radix-dialog-content]::-webkit-scrollbar {
  width: 8px !important;
}

[data-radix-dialog-content]::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px !important;
}

[data-radix-dialog-content]::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
}

[data-radix-dialog-content]::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* Estilos específicos para modais de pagamento PIX */
.payment-modal [data-radix-dialog-content],
[data-radix-dialog-content][class*="payment"] {
  background-color: #1E293B !important;
}

/* Estilos específicos para modais de bilhetes */
.bilhetes-modal [data-radix-dialog-content],
[data-radix-dialog-content][class*="bilhete"] {
  background-color: #1E293B !important;
}

/* Garantir que elementos com fundo branco forçado sejam ajustados */
[data-radix-dialog-content] .bg-white {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-radix-dialog-content] .bg-gray-50 {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

[data-radix-dialog-content] .bg-gray-100 {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
