import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'
import bcrypt from 'bcryptjs'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const userId = searchParams.get('userId') || '1' // Por enquanto usar usuário padrão

    // Buscar dados do usuário
    const users = await executeQuery(`
      SELECT 
        id,
        nome,
        email,
        telefone,
        endereco,
        cpf,
        data_nascimento,
        data_cadastro,
        status,
        tipo
      FROM usuarios
      WHERE id = ?
    `, [userId])

    if (users.length === 0) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      )
    }

    const user = users[0]

    // Formatar dados para o frontend
    const profile = {
      id: user.id,
      nome: user.nome,
      email: user.email,
      telefone: user.telefone || '',
      endereco: user.endereco || '',
      cpf: user.cpf || '',
      data_nascimento: user.data_nascimento || '',
      data_cadastro: user.data_cadastro,
      status: user.status,
      tipo: user.tipo
    }

    return NextResponse.json(profile)

  } catch (error) {
    console.error('❌ Erro ao buscar perfil:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      userId = 1,
      nome,
      telefone,
      endereco,
      senha_atual,
      nova_senha
    } = body

    // Validar dados obrigatórios
    if (!nome) {
      return NextResponse.json(
        { error: 'Nome é obrigatório' },
        { status: 400 }
      )
    }

    // Se está alterando senha, validar senha atual
    if (nova_senha) {
      if (!senha_atual) {
        return NextResponse.json(
          { error: 'Senha atual é obrigatória para alterar a senha' },
          { status: 400 }
        )
      }

      // Buscar senha atual do usuário
      const users = await executeQuery(`
        SELECT senha_hash FROM usuarios WHERE id = ?
      `, [userId])

      if (users.length === 0) {
        return NextResponse.json(
          { error: 'Usuário não encontrado' },
          { status: 404 }
        )
      }

      const user = users[0]
      const senhaValida = await bcrypt.compare(senha_atual, user.senha_hash)

      if (!senhaValida) {
        return NextResponse.json(
          { error: 'Senha atual incorreta' },
          { status: 400 }
        )
      }

      // Hash da nova senha
      const novaSenhaHash = await bcrypt.hash(nova_senha, 10)

      // Atualizar com nova senha
      await executeQuery(`
        UPDATE usuarios 
        SET nome = ?, telefone = ?, endereco = ?, senha_hash = ?
        WHERE id = ?
      `, [nome, telefone, endereco, novaSenhaHash, userId])
    } else {
      // Atualizar sem alterar senha
      await executeQuery(`
        UPDATE usuarios 
        SET nome = ?, telefone = ?, endereco = ?
        WHERE id = ?
      `, [nome, telefone, endereco, userId])
    }

    return NextResponse.json({
      success: true,
      message: 'Perfil atualizado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao atualizar perfil:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
