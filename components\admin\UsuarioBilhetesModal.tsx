"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, DialogDescription, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Loader2, Receipt, Calendar, DollarSign, Target, Trophy, Clock } from "lucide-react"
import TeamLogo from "@/components/TeamLogo"

interface Aposta {
  id: number
  resultado: "casa" | "empate" | "fora"
  acertou?: boolean
  created_at: string
  jogo_id: number
  data_jogo: string
  jogo_status: string
  resultado_casa?: number
  resultado_fora?: number
  time_casa_nome: string
  time_casa_curto: string
  time_casa_logo: string
  time_fora_nome: string
  time_fora_curto: string
  time_fora_logo: string
  campeonato_nome: string
  campeonato_logo?: string
}

interface Bilhete {
  id: number
  codigo: string
  usuario_nome: string
  usuario_email: string
  usuario_cpf: string
  valor_total: number
  quantidade_apostas: number
  status: "pendente" | "pago" | "cancelado" | "expirado"
  transaction_id?: string
  end_to_end_id?: string
  data_expiracao?: string
  created_at: string
  updated_at: string
  apostas: Aposta[]
  total_apostas: number
}

interface Stats {
  total_bilhetes: number
  bilhetes_pendentes: number
  bilhetes_pagos: number
  bilhetes_cancelados: number
  valor_total: number
  valor_pago: number
}

interface UsuarioBilhetesModalProps {
  isOpen: boolean
  onClose: () => void
  userId: number
  userName: string
}

export default function UsuarioBilhetesModal({ isOpen, onClose, userId, userName }: UsuarioBilhetesModalProps) {
  const [bilhetes, setBilhetes] = useState<Bilhete[]>([])
  const [stats, setStats] = useState<Stats | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedBilhete, setSelectedBilhete] = useState<Bilhete | null>(null)

  useEffect(() => {
    if (isOpen && userId) {
      fetchBilhetes()
    }
  }, [isOpen, userId])

  const fetchBilhetes = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/usuarios/${userId}/bilhetes`)
      
      if (!response.ok) {
        throw new Error('Erro ao carregar bilhetes')
      }

      const data = await response.json()
      setBilhetes(data.bilhetes || [])
      setStats(data.stats || null)
    } catch (error) {
      console.error('Erro ao carregar bilhetes:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pago': return 'bg-green-100 text-green-800'
      case 'pendente': return 'bg-yellow-100 text-yellow-800'
      case 'cancelado': return 'bg-red-100 text-red-800'
      case 'expirado': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getResultadoLabel = (resultado: string) => {
    switch (resultado) {
      case 'casa': return 'Casa'
      case 'empate': return 'Empate'
      case 'fora': return 'Fora'
      default: return resultado
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('pt-BR')
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  if (selectedBilhete) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden w-[95vw] sm:w-full">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Receipt className="h-5 w-5 flex-shrink-0" />
              <span className="truncate">Detalhes do Bilhete {selectedBilhete.codigo}</span>
            </DialogTitle>
            <DialogDescription className="text-sm">
              Apostas realizadas por {selectedBilhete.usuario_nome}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Info do Bilhete */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informações do Bilhete</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Status</p>
                  <Badge className={getStatusColor(selectedBilhete.status)}>
                    {selectedBilhete.status.toUpperCase()}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Valor</p>
                  <p className="font-semibold">{formatCurrency(selectedBilhete.valor_total)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Apostas</p>
                  <p className="font-semibold">{selectedBilhete.total_apostas}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Data</p>
                  <p className="font-semibold">{formatDateTime(selectedBilhete.created_at)}</p>
                </div>
              </CardContent>
            </Card>

            {/* Lista de Apostas */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Apostas Realizadas</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {selectedBilhete.apostas.map((aposta, index) => (
                      <div key={aposta.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{aposta.campeonato_nome}</Badge>
                            <span className="text-sm text-gray-600 flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {formatDateTime(aposta.data_jogo)}
                            </span>
                          </div>
                          <Badge className={getStatusColor(aposta.jogo_status)}>
                            {aposta.jogo_status}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <TeamLogo 
                                src={aposta.time_casa_logo} 
                                alt={aposta.time_casa_nome}
                                size="sm"
                              />
                              <span className="font-medium">{aposta.time_casa_curto}</span>
                            </div>
                            
                            <div className="text-center">
                              <div className="text-sm text-gray-600">vs</div>
                              {aposta.resultado_casa !== null && aposta.resultado_fora !== null && (
                                <div className="font-bold">
                                  {aposta.resultado_casa} - {aposta.resultado_fora}
                                </div>
                              )}
                            </div>

                            <div className="flex items-center gap-2">
                              <span className="font-medium">{aposta.time_fora_curto}</span>
                              <TeamLogo 
                                src={aposta.time_fora_logo} 
                                alt={aposta.time_fora_nome}
                                size="sm"
                              />
                            </div>
                          </div>

                          <div className="text-right">
                            <div className="text-sm text-gray-600">Aposta</div>
                            <Badge variant="secondary">
                              {getResultadoLabel(aposta.resultado)}
                            </Badge>
                            {aposta.acertou !== undefined && (
                              <div className="mt-1">
                                <Badge className={aposta.acertou ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                                  {aposta.acertou ? 'Acertou' : 'Errou'}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setSelectedBilhete(null)}>
              Voltar
            </Button>
            <Button onClick={onClose}>
              Fechar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden w-[95vw] sm:w-full">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <Receipt className="h-5 w-5 flex-shrink-0" />
            <span className="truncate">Bilhetes de {userName}</span>
          </DialogTitle>
          <DialogDescription className="text-sm">
            Visualize todos os bilhetes e apostas realizadas por este usuário
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Estatísticas */}
            {stats && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Total Bilhetes</p>
                        <p className="text-2xl font-bold">{stats.total_bilhetes}</p>
                      </div>
                      <Receipt className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Pagos</p>
                        <p className="text-2xl font-bold text-green-600">{stats.bilhetes_pagos}</p>
                      </div>
                      <DollarSign className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Pendentes</p>
                        <p className="text-2xl font-bold text-yellow-600">{stats.bilhetes_pendentes}</p>
                      </div>
                      <Clock className="h-8 w-8 text-yellow-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Valor Total</p>
                        <p className="text-2xl font-bold">{formatCurrency(stats.valor_total)}</p>
                      </div>
                      <Trophy className="h-8 w-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Lista de Bilhetes */}
            <Card>
              <CardHeader>
                <CardTitle>Lista de Bilhetes</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  {bilhetes.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      Nenhum bilhete encontrado para este usuário
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {bilhetes.map((bilhete) => (
                        <div key={bilhete.id} className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                             onClick={() => setSelectedBilhete(bilhete)}>
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="font-semibold">{bilhete.codigo}</span>
                                <Badge className={getStatusColor(bilhete.status)}>
                                  {bilhete.status.toUpperCase()}
                                </Badge>
                              </div>
                              <div className="text-sm text-gray-600">
                                {bilhete.total_apostas} apostas • {formatDateTime(bilhete.created_at)}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">{formatCurrency(bilhete.valor_total)}</div>
                              <div className="text-sm text-gray-600">
                                {bilhete.transaction_id && `ID: ${bilhete.transaction_id.slice(-8)}`}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        )}

        <div className="flex justify-end">
          <Button onClick={onClose}>
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
