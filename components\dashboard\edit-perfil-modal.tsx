"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Lock,
  Save,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface UserProfile {
  id: number
  nome: string
  email: string
  telefone?: string
  endereco?: string
  cpf?: string
  data_nascimento?: string
  data_cadastro: string
  status: string
  tipo: string
}

interface FormData {
  nome: string
  telefone: string
  endereco: string
  senha_atual: string
  nova_senha: string
  confirmar_senha: string
}

interface EditPerfilModalProps {
  isOpen: boolean
  onClose: () => void
  profile: UserProfile
  onSave: (data: FormData) => Promise<void>
}

export function EditPerfilModal({ isOpen, onClose, profile, onSave }: EditPerfilModalProps) {
  const [formData, setFormData] = useState<FormData>({
    nome: profile.nome || "",
    telefone: profile.telefone || "",
    endereco: profile.endereco || "",
    senha_atual: "",
    nova_senha: "",
    confirmar_senha: ""
  })
  const [saving, setSaving] = useState(false)

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.nova_senha && formData.nova_senha !== formData.confirmar_senha) {
      toast.error("As senhas não coincidem")
      return
    }

    if (formData.nova_senha && !formData.senha_atual) {
      toast.error("Informe a senha atual para alterar a senha")
      return
    }

    try {
      setSaving(true)
      await onSave(formData)
      toast.success("Perfil atualizado com sucesso!")
      onClose()
    } catch (error: any) {
      toast.error(error.message || "Erro ao salvar perfil")
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <User className="h-5 w-5" />
            Editar Perfil
          </DialogTitle>
          <DialogDescription>
            Atualize suas informações pessoais e configurações de segurança
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSave} className="space-y-6">
          <Tabs defaultValue="dados" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="dados">Dados Pessoais</TabsTrigger>
              <TabsTrigger value="seguranca">Segurança</TabsTrigger>
            </TabsList>

            {/* Dados Pessoais */}
            <TabsContent value="dados" className="space-y-4 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Informações Pessoais</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="nome" className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Nome Completo
                      </Label>
                      <Input
                        id="nome"
                        value={formData.nome}
                        onChange={(e) => handleInputChange("nome", e.target.value)}
                        placeholder="Seu nome completo"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email" className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Email
                      </Label>
                      <Input
                        id="email"
                        value={profile.email}
                        disabled
                        className="bg-gray-50"
                      />
                      <p className="text-xs text-gray-500">
                        O email não pode ser alterado
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="telefone" className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Telefone
                    </Label>
                    <Input
                      id="telefone"
                      value={formData.telefone}
                      onChange={(e) => handleInputChange("telefone", e.target.value)}
                      placeholder="(11) 99999-9999"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="endereco" className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Endereço
                    </Label>
                    <Input
                      id="endereco"
                      value={formData.endereco}
                      onChange={(e) => handleInputChange("endereco", e.target.value)}
                      placeholder="Seu endereço completo"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Segurança */}
            <TabsContent value="seguranca" className="space-y-4 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Alterar Senha</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="senha_atual" className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      Senha Atual
                    </Label>
                    <Input
                      id="senha_atual"
                      type="password"
                      value={formData.senha_atual}
                      onChange={(e) => handleInputChange("senha_atual", e.target.value)}
                      placeholder="Digite sua senha atual"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="nova_senha">Nova Senha</Label>
                      <Input
                        id="nova_senha"
                        type="password"
                        value={formData.nova_senha}
                        onChange={(e) => handleInputChange("nova_senha", e.target.value)}
                        placeholder="Digite a nova senha"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmar_senha">Confirmar Senha</Label>
                      <Input
                        id="confirmar_senha"
                        type="password"
                        value={formData.confirmar_senha}
                        onChange={(e) => handleInputChange("confirmar_senha", e.target.value)}
                        placeholder="Confirme a nova senha"
                      />
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Dica:</strong> Use uma senha forte com pelo menos 8 caracteres, 
                      incluindo letras maiúsculas, minúsculas, números e símbolos.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="w-full sm:w-auto"
              disabled={saving}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={saving}
              className="w-full sm:flex-1"
            >
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Alterações
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
