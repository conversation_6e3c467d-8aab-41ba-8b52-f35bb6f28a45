"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Calendar, 
  DollarSign, 
  Eye, 
  Receipt,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { cn } from "@/lib/utils"

interface Bilhete {
  id: number
  codigo: string
  bolao: string
  jogos: number
  valor: number
  data: string
  status: "pendente" | "pago" | "cancelado" | "em_andamento" | "perdida" | "ganha"
  premio?: number
}

interface BilhetesCardProps {
  bilhetes: Bilhete[]
  onViewDetails?: (bilhete: Bilhete) => void
  className?: string
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case "pago":
    case "ganha":
      return {
        variant: "default" as const,
        className: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
        label: "Pago"
      }
    case "pendente":
      return {
        variant: "secondary" as const,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: Clock,
        label: "Pendente"
      }
    case "em_andamento":
      return {
        variant: "outline" as const,
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: AlertCircle,
        label: "Em Andamento"
      }
    case "cancelado":
    case "perdida":
      return {
        variant: "destructive" as const,
        className: "bg-red-100 text-red-800 border-red-200",
        icon: XCircle,
        label: "Cancelado"
      }
    default:
      return {
        variant: "outline" as const,
        className: "bg-gray-100 text-gray-800 border-gray-200",
        icon: AlertCircle,
        label: status
      }
  }
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("pt-BR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  })
}

export function BilhetesCard({ bilhetes, onViewDetails, className }: BilhetesCardProps) {
  if (bilhetes.length === 0) {
    return (
      <div className={cn("text-center py-12", className)}>
        <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum bilhete encontrado</h3>
        <p className="text-gray-600">Você ainda não fez nenhuma aposta.</p>
      </div>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      {bilhetes.map((bilhete) => {
        const statusConfig = getStatusConfig(bilhete.status)
        const StatusIcon = statusConfig.icon

        return (
          <Card key={bilhete.id} className="hover:shadow-md transition-all duration-200 border-l-4 border-l-blue-500">
            <CardContent className="p-4">
              {/* Header - Mobile First */}
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-4">
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 p-2 rounded-lg flex-shrink-0">
                    <Receipt className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-gray-900 text-base sm:text-lg truncate">
                      {bilhete.codigo}
                    </h3>
                    <p className="text-sm text-gray-600 truncate">{bilhete.bolao}</p>
                  </div>
                </div>
                
                <Badge className={cn("self-start", statusConfig.className)}>
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>

              {/* Stats Grid - Responsive */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-xs text-gray-500 font-medium">Data</span>
                  </div>
                  <p className="text-sm font-semibold text-gray-900">
                    {formatDate(bilhete.data)}
                  </p>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Receipt className="h-4 w-4 text-gray-500" />
                    <span className="text-xs text-gray-500 font-medium">Jogos</span>
                  </div>
                  <p className="text-sm font-semibold text-gray-900">
                    {bilhete.jogos}
                  </p>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span className="text-xs text-gray-500 font-medium">Valor</span>
                  </div>
                  <p className="text-sm font-semibold text-green-600">
                    {formatCurrency(bilhete.valor)}
                  </p>
                </div>

                {bilhete.premio && (
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-xs text-green-600 font-medium">Prêmio</span>
                    </div>
                    <p className="text-sm font-semibold text-green-600">
                      {formatCurrency(bilhete.premio)}
                    </p>
                  </div>
                )}
              </div>

              {/* Actions */}
              {onViewDetails && (
                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewDetails(bilhete)}
                    className="w-full sm:w-auto"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Ver Detalhes
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
