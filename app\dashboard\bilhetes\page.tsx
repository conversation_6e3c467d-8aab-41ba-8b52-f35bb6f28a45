"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BilhetesCard } from "@/components/dashboard/bilhetes-card"
import {
  Receipt,
  Search,
  Download,
  Eye,
  Calendar,
  DollarSign,
  Target,
  CheckCircle,
  XCircle,
  Clock,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface Bilhete {
  id: number
  codigo: string
  bolao: string
  jogos: number
  valor: number
  data: string
  status: "pendente" | "ganha" | "perdida" | "em_andamento"
  premio?: number
}

export default function BilhetesPage() {
  const [bilhetes, setBilhetes] = useState<Bilhete[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("todos")

  useEffect(() => {
    loadBilhetes()
  }, [])

  const loadBilhetes = async () => {
    try {
      setLoading(true)

      // Obter dados do usuário do localStorage
      const userData = localStorage.getItem("user")
      const userId = userData ? JSON.parse(userData).id : 1

      // Buscar dados reais dos bilhetes
      const response = await fetch(`/api/bilhetes?userId=${userId}`)
      if (!response.ok) {
        throw new Error('Erro ao carregar bilhetes')
      }

      const bilhetesData = await response.json()
      setBilhetes(bilhetesData)

      // Calcular estatísticas
      const totalApostas = bilhetesData.length
      const apostasGanhas = bilhetesData.filter((b: Bilhete) => b.status === 'pago' || b.status === 'ganho').length
      const valorTotal = bilhetesData.reduce((sum: number, b: Bilhete) => sum + b.valor_total, 0)
      const pendentes = bilhetesData.filter((b: Bilhete) => b.status === 'pendente').length

      setStats({
        totalApostas,
        apostasGanhas,
        valorTotal,
        pendentes
      })
    } catch (error) {
      console.error("Erro ao carregar bilhetes:", error)
      toast.error("Erro ao carregar bilhetes")
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ganha":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Ganha
          </Badge>
        )
      case "perdida":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Perdida
          </Badge>
        )
      case "em_andamento":
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Em Andamento
          </Badge>
        )
      case "pendente":
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const filteredBilhetes = bilhetes.filter(bilhete => {
    const matchesSearch = bilhete.codigo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bilhete.bolao.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "todos" || bilhete.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const calcularEstatisticas = () => {
    const total = bilhetes.length
    const ganhas = bilhetes.filter(b => b.status === "ganha").length
    const perdidas = bilhetes.filter(b => b.status === "perdida").length
    const emAndamento = bilhetes.filter(b => b.status === "em_andamento").length
    const totalInvestido = bilhetes.reduce((sum, b) => sum + b.valor, 0)
    const totalGanho = bilhetes.filter(b => b.premio).reduce((sum, b) => sum + (b.premio || 0), 0)

    return { total, ganhas, perdidas, emAndamento, totalInvestido, totalGanho }
  }

  const stats = calcularEstatisticas()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando bilhetes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Meus Bilhetes</h1>
        <p className="text-gray-600 mt-2">Acompanhe todos os seus bilhetes de apostas</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Bilhetes</p>
                <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <Receipt className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Bilhetes Ganhos</p>
                <p className="text-3xl font-bold text-green-600">{stats.ganhas}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Investido</p>
                <p className="text-3xl font-bold text-orange-600">{formatCurrency(stats.totalInvestido)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Ganho</p>
                <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.totalGanho)}</p>
              </div>
              <Target className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por código ou bolão..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos</SelectItem>
                <SelectItem value="pendente">Pendente</SelectItem>
                <SelectItem value="em_andamento">Em Andamento</SelectItem>
                <SelectItem value="ganha">Ganha</SelectItem>
                <SelectItem value="perdida">Perdida</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="w-full sm:w-auto">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Bilhetes */}
      <div>
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Seus Bilhetes</h2>
          <p className="text-gray-600 text-sm">
            {filteredBilhetes.length} bilhete{filteredBilhetes.length !== 1 ? 's' : ''} encontrado{filteredBilhetes.length !== 1 ? 's' : ''}
          </p>
        </div>

        <BilhetesCard
          bilhetes={filteredBilhetes}
          onViewDetails={(bilhete) => {
            // Implementar modal de detalhes se necessário
            console.log('Ver detalhes do bilhete:', bilhete)
          }}
        />
      </div>
    </div>
  )
}
