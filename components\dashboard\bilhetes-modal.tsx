"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Receipt, 
  Calendar, 
  DollarSign, 
  Trophy,
  Target,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Download,
  Share,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"

interface Bilhete {
  id: number
  codigo: string
  bolao: string
  jogos: number
  valor: number
  data: string
  status: "pendente" | "pago" | "cancelado" | "em_andamento" | "perdida" | "ganha"
  premio?: number
  detalhes?: {
    numeros?: number[]
    modalidade?: string
    concurso?: string
    dataJogo?: string
  }
}

interface BilhetesModalProps {
  isOpen: boolean
  onClose: () => void
  bilhetes: Bilhete[]
  title?: string
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case "pago":
    case "ganha":
      return {
        variant: "default" as const,
        className: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
        label: "Pago"
      }
    case "pendente":
      return {
        variant: "secondary" as const,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: Clock,
        label: "Pendente"
      }
    case "em_andamento":
      return {
        variant: "outline" as const,
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: AlertCircle,
        label: "Em Andamento"
      }
    case "cancelado":
    case "perdida":
      return {
        variant: "destructive" as const,
        className: "bg-red-100 text-red-800 border-red-200",
        icon: XCircle,
        label: "Cancelado"
      }
    default:
      return {
        variant: "outline" as const,
        className: "bg-gray-100 text-gray-800 border-gray-200",
        icon: AlertCircle,
        label: status
      }
  }
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("pt-BR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  })
}

export function BilhetesModal({ isOpen, onClose, bilhetes, title = "Meus Bilhetes" }: BilhetesModalProps) {
  const [selectedBilhete, setSelectedBilhete] = useState<Bilhete | null>(null)

  const handleBilheteClick = (bilhete: Bilhete) => {
    setSelectedBilhete(bilhete)
  }

  const handleBack = () => {
    setSelectedBilhete(null)
  }

  const handleDownload = (bilhete: Bilhete) => {
    // Implementar download do bilhete
    console.log('Download bilhete:', bilhete.codigo)
  }

  const handleShare = (bilhete: Bilhete) => {
    // Implementar compartilhamento
    if (navigator.share) {
      navigator.share({
        title: `Bilhete ${bilhete.codigo}`,
        text: `Confira meu bilhete de aposta: ${bilhete.codigo}`,
        url: window.location.href
      })
    }
  }

  if (selectedBilhete) {
    const statusConfig = getStatusConfig(selectedBilhete.status)
    const StatusIcon = statusConfig.icon

    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
          <DialogHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  className="p-2"
                >
                  ←
                </Button>
                <div>
                  <DialogTitle className="text-xl">
                    Bilhete {selectedBilhete.codigo}
                  </DialogTitle>
                  <DialogDescription>
                    Detalhes completos do bilhete
                  </DialogDescription>
                </div>
              </div>
              <Badge className={cn("ml-auto", statusConfig.className)}>
                <StatusIcon className="h-3 w-3 mr-1" />
                {statusConfig.label}
              </Badge>
            </div>
          </DialogHeader>

          <div className="space-y-6">
            {/* Informações Principais */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informações do Bilhete</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Receipt className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600">Código</span>
                    </div>
                    <p className="text-lg font-semibold">{selectedBilhete.codigo}</p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600">Bolão</span>
                    </div>
                    <p className="text-lg font-semibold">{selectedBilhete.bolao}</p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600">Data</span>
                    </div>
                    <p className="text-lg font-semibold">{formatDate(selectedBilhete.data)}</p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600">Valor</span>
                    </div>
                    <p className="text-lg font-semibold text-green-600">
                      {formatCurrency(selectedBilhete.valor)}
                    </p>
                  </div>
                </div>

                {selectedBilhete.premio && (
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Trophy className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-green-700">Prêmio Ganho</span>
                    </div>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(selectedBilhete.premio)}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Detalhes do Jogo */}
            {selectedBilhete.detalhes && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Detalhes do Jogo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedBilhete.detalhes.numeros && (
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-2">Números Apostados</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedBilhete.detalhes.numeros.map((numero, index) => (
                          <div
                            key={index}
                            className="w-10 h-10 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-semibold text-sm"
                          >
                            {numero}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {selectedBilhete.detalhes.modalidade && (
                      <div>
                        <p className="text-sm font-medium text-gray-600">Modalidade</p>
                        <p className="text-lg">{selectedBilhete.detalhes.modalidade}</p>
                      </div>
                    )}

                    {selectedBilhete.detalhes.concurso && (
                      <div>
                        <p className="text-sm font-medium text-gray-600">Concurso</p>
                        <p className="text-lg">{selectedBilhete.detalhes.concurso}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Ações */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => handleDownload(selectedBilhete)}
                className="w-full sm:w-auto"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button
                variant="outline"
                onClick={() => handleShare(selectedBilhete)}
                className="w-full sm:w-auto"
              >
                <Share className="h-4 w-4 mr-2" />
                Compartilhar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Receipt className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            Histórico de todas as suas apostas ({bilhetes.length} bilhete{bilhetes.length !== 1 ? 's' : ''})
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3">
          {bilhetes.length === 0 ? (
            <div className="text-center py-12">
              <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum bilhete encontrado</h3>
              <p className="text-gray-600">Você ainda não fez nenhuma aposta.</p>
            </div>
          ) : (
            bilhetes.map((bilhete) => {
              const statusConfig = getStatusConfig(bilhete.status)
              const StatusIcon = statusConfig.icon

              return (
                <Card 
                  key={bilhete.id} 
                  className="hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-l-blue-500"
                  onClick={() => handleBilheteClick(bilhete)}
                >
                  <CardContent className="p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-blue-100 p-2 rounded-lg flex-shrink-0">
                          <Receipt className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className="font-semibold text-gray-900 truncate">
                            {bilhete.codigo}
                          </h3>
                          <p className="text-sm text-gray-600 truncate">{bilhete.bolao}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between sm:justify-end gap-4">
                        <div className="text-right">
                          <p className="text-sm font-semibold text-green-600">
                            {formatCurrency(bilhete.valor)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(bilhete.data)}
                          </p>
                        </div>
                        <Badge className={cn("self-start", statusConfig.className)}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusConfig.label}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
