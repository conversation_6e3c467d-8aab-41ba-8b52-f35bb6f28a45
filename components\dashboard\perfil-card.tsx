"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Shield,
  Edit,
  Trophy,
  Target,
  DollarSign
} from "lucide-react"
import { cn } from "@/lib/utils"

interface UserProfile {
  id: number
  nome: string
  email: string
  telefone?: string
  endereco?: string
  cpf?: string
  data_nascimento?: string
  data_cadastro: string
  status: string
  tipo: string
}

interface UserStats {
  totalApostas: number
  apostasGanhas: number
  totalInvestido: number
  totalGanho: number
}

interface PerfilCardProps {
  profile: UserProfile
  stats?: UserStats
  onEdit?: () => void
  className?: string
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "ativo":
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          <Shield className="h-3 w-3 mr-1" />
          Ativo
        </Badge>
      )
    case "inativo":
      return (
        <Badge variant="secondary">
          <Shield className="h-3 w-3 mr-1" />
          Inativo
        </Badge>
      )
    case "bloqueado":
      return (
        <Badge variant="destructive">
          <Shield className="h-3 w-3 mr-1" />
          Bloqueado
        </Badge>
      )
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

const getTipoBadge = (tipo: string) => {
  switch (tipo) {
    case "admin":
      return (
        <Badge className="bg-purple-100 text-purple-800 border-purple-200">
          <User className="h-3 w-3 mr-1" />
          Administrador
        </Badge>
      )
    case "cambista":
      return (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          <User className="h-3 w-3 mr-1" />
          Cambista
        </Badge>
      )
    case "usuario":
      return (
        <Badge variant="outline">
          <User className="h-3 w-3 mr-1" />
          Usuário
        </Badge>
      )
    default:
      return <Badge variant="outline">{tipo}</Badge>
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("pt-BR")
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value)
}

export function PerfilCard({ profile, stats, onEdit, className }: PerfilCardProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {/* Profile Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-center gap-6">
            {/* Avatar and Basic Info */}
            <div className="flex items-center gap-4">
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-4 rounded-full flex-shrink-0">
                <User className="h-8 w-8 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">
                  {profile.nome}
                </h2>
                <p className="text-gray-600 truncate">{profile.email}</p>
                <div className="flex flex-wrap items-center gap-2 mt-2">
                  {getStatusBadge(profile.status)}
                  {getTipoBadge(profile.tipo)}
                  <Badge variant="outline" className="text-xs">
                    <Calendar className="h-3 w-3 mr-1" />
                    Desde {formatDate(profile.data_cadastro)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Edit Button */}
            {onEdit && (
              <Button 
                onClick={onEdit}
                variant="outline"
                className="w-full sm:w-auto"
              >
                <Edit className="h-4 w-4 mr-2" />
                Editar Perfil
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informações de Contato</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Mail className="h-5 w-5 text-gray-400 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-600">Email</p>
                <p className="text-sm text-gray-900 truncate">{profile.email}</p>
              </div>
            </div>

            {profile.telefone && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Phone className="h-5 w-5 text-gray-400 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-600">Telefone</p>
                  <p className="text-sm text-gray-900">{profile.telefone}</p>
                </div>
              </div>
            )}

            {profile.endereco && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg sm:col-span-2">
                <MapPin className="h-5 w-5 text-gray-400 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-600">Endereço</p>
                  <p className="text-sm text-gray-900">{profile.endereco}</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Estatísticas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Target className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-600">{stats.totalApostas}</p>
                <p className="text-xs text-gray-600">Total Apostas</p>
              </div>

              <div className="text-center p-4 bg-green-50 rounded-lg">
                <Trophy className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-600">{stats.apostasGanhas}</p>
                <p className="text-xs text-gray-600">Apostas Ganhas</p>
              </div>

              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <p className="text-lg font-bold text-orange-600">
                  {formatCurrency(stats.totalInvestido)}
                </p>
                <p className="text-xs text-gray-600">Investido</p>
              </div>

              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <p className="text-lg font-bold text-purple-600">
                  {formatCurrency(stats.totalGanho)}
                </p>
                <p className="text-xs text-gray-600">Ganho</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
