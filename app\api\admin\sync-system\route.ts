import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Iniciando sincronização do sistema...')

    // 1. Verificar campeonatos disponíveis
    const campeonatosResponse = await fetch('https://api.football-data.org/v4/competitions', {
      headers: {
        'X-Auth-Token': process.env.FOOTBALL_API_KEY || 'demo'
      }
    })

    if (!campeonatosResponse.ok) {
      throw new Error('Erro ao buscar campeonatos da API')
    }

    const campeonatosData = await campeonatosResponse.json()
    console.log(`📊 ${campeonatosData.competitions?.length || 0} campeonatos encontrados`)

    // 2. Sincronizar campeonatos principais
    const campeonatosPrincipais = ['PL', 'PD', 'SA', 'BL1', 'FL1', 'BSA'] // Premier League, La Liga, Serie A, Bundesliga, Ligue 1, Brasileirão
    let campeonatosSincronizados = 0

    for (const codigo of campeonatosPrincipais) {
      try {
        const campeonato = campeonatosData.competitions?.find((c: any) => c.code === codigo)
        if (campeonato) {
          // Verificar se já existe
          const existingCampeonato = await executeQuery(`
            SELECT id FROM campeonatos WHERE codigo = ?
          `, [codigo])

          if (existingCampeonato.length === 0) {
            // Inserir novo campeonato
            await executeQuery(`
              INSERT INTO campeonatos (nome, codigo, pais, temporada, status)
              VALUES (?, ?, ?, ?, 'ativo')
            `, [
              campeonato.name,
              campeonato.code,
              campeonato.area?.name || 'Internacional',
              new Date().getFullYear().toString()
            ])
            campeonatosSincronizados++
          }
        }
      } catch (error) {
        console.error(`❌ Erro ao sincronizar campeonato ${codigo}:`, error)
      }
    }

    // 3. Buscar partidas recentes
    let partidasSincronizadas = 0
    for (const codigo of campeonatosPrincipais) {
      try {
        const partidasResponse = await fetch(`https://api.football-data.org/v4/competitions/${codigo}/matches?status=SCHEDULED&limit=20`, {
          headers: {
            'X-Auth-Token': process.env.FOOTBALL_API_KEY || 'demo'
          }
        })

        if (partidasResponse.ok) {
          const partidasData = await partidasResponse.json()
          
          for (const partida of partidasData.matches || []) {
            try {
              // Verificar se a partida já existe
              const existingPartida = await executeQuery(`
                SELECT id FROM jogos WHERE external_id = ?
              `, [partida.id])

              if (existingPartida.length === 0) {
                // Buscar ou criar times
                const timeCasaId = await getOrCreateTeam(partida.homeTeam)
                const timeForaId = await getOrCreateTeam(partida.awayTeam)
                
                // Buscar campeonato
                const campeonato = await executeQuery(`
                  SELECT id FROM campeonatos WHERE codigo = ?
                `, [codigo])

                if (campeonato.length > 0) {
                  // Inserir partida
                  await executeQuery(`
                    INSERT INTO jogos (
                      campeonato_id, 
                      time_casa_id, 
                      time_fora_id, 
                      data_jogo, 
                      status,
                      external_id
                    ) VALUES (?, ?, ?, ?, 'agendado', ?)
                  `, [
                    campeonato[0].id,
                    timeCasaId,
                    timeForaId,
                    new Date(partida.utcDate),
                    partida.id
                  ])
                  partidasSincronizadas++
                }
              }
            } catch (error) {
              console.error(`❌ Erro ao sincronizar partida ${partida.id}:`, error)
            }
          }
        }
      } catch (error) {
        console.error(`❌ Erro ao buscar partidas do campeonato ${codigo}:`, error)
      }
    }

    // 4. Atualizar estatísticas do sistema
    const stats = await executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM campeonatos WHERE status = 'ativo') as total_campeonatos,
        (SELECT COUNT(*) FROM jogos WHERE status = 'agendado') as total_jogos,
        (SELECT COUNT(*) FROM times) as total_times,
        (SELECT COUNT(*) FROM usuarios WHERE status = 'ativo') as total_usuarios
    `)

    const systemStats = stats[0] || {}

    console.log('✅ Sincronização concluída:', {
      campeonatosSincronizados,
      partidasSincronizadas,
      stats: systemStats
    })

    return NextResponse.json({
      success: true,
      message: 'Sistema sincronizado com sucesso',
      data: {
        campeonatos_sincronizados: campeonatosSincronizados,
        partidas_sincronizadas: partidasSincronizadas,
        estatisticas: systemStats
      }
    })

  } catch (error) {
    console.error('❌ Erro na sincronização do sistema:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    )
  }
}

async function getOrCreateTeam(teamData: any): Promise<number> {
  try {
    // Verificar se o time já existe
    const existingTeam = await executeQuery(`
      SELECT id FROM times WHERE external_id = ?
    `, [teamData.id])

    if (existingTeam.length > 0) {
      return existingTeam[0].id
    }

    // Criar novo time
    const result = await executeQuery(`
      INSERT INTO times (nome, nome_curto, external_id)
      VALUES (?, ?, ?)
    `, [
      teamData.name,
      teamData.shortName || teamData.name.substring(0, 3).toUpperCase(),
      teamData.id
    ])

    return (result as any).insertId
  } catch (error) {
    console.error('❌ Erro ao criar/buscar time:', error)
    throw error
  }
}
