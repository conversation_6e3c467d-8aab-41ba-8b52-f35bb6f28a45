"use client"

import { useState, useEffect } from "react"
import { PerfilCard } from "@/components/dashboard/perfil-card"
import { EditPerfilModal } from "@/components/dashboard/edit-perfil-modal"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

interface UserProfile {
  id: number
  nome: string
  email: string
  telefone: string
  endereco: string
  cpf: string
  data_nascimento: string
  data_cadastro: string
  status: string
  tipo: string
}

interface UserStats {
  totalApostas: number
  apostasGanhas: number
  totalInvestido: number
  totalGanho: number
}

export default function PerfilPage() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [editModalOpen, setEditModalOpen] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)

      // Obter dados do usuário do localStorage
      const userData = localStorage.getItem("user")
      const userId = userData ? JSON.parse(userData).id : 1

      // Buscar dados do perfil
      const profileResponse = await fetch(`/api/dashboard/perfil?userId=${userId}`)
      if (!profileResponse.ok) {
        throw new Error('Erro ao carregar perfil')
      }
      const profileData = await profileResponse.json()
      setProfile(profileData)

      // Buscar estatísticas do usuário
      const statsResponse = await fetch(`/api/dashboard/stats?userId=${userId}`)
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }
    } catch (error) {
      console.error("Erro ao carregar dados:", error)
      toast.error("Erro ao carregar dados do perfil")
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProfile = async (formData: any) => {
    // Obter dados do usuário do localStorage
    const userData = localStorage.getItem("user")
    const userId = userData ? JSON.parse(userData).id : 1

    // Enviar dados para API
    const response = await fetch('/api/dashboard/perfil', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        nome: formData.nome,
        telefone: formData.telefone,
        endereco: formData.endereco,
        senha_atual: formData.senha_atual,
        nova_senha: formData.nova_senha
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Erro ao salvar perfil')
    }

    // Atualizar perfil local
    if (profile) {
      const updatedProfile = {
        ...profile,
        nome: formData.nome,
        telefone: formData.telefone,
        endereco: formData.endereco
      }
      setProfile(updatedProfile)

      // Atualizar localStorage
      if (userData) {
        const user = JSON.parse(userData)
        user.nome = formData.nome
        localStorage.setItem("user", JSON.stringify(user))
      }
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando perfil...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Erro ao carregar perfil</p>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Header */}
      <div className="text-center sm:text-left">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Meu Perfil</h1>
        <p className="text-gray-600 mt-2">Gerencie suas informações pessoais e configurações</p>
      </div>

      {/* Profile Card */}
      <PerfilCard
        profile={profile}
        stats={stats || undefined}
        onEdit={() => setEditModalOpen(true)}
      />

      {/* Edit Modal */}
      <EditPerfilModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        profile={profile}
        onSave={handleSaveProfile}
      />
    </div>
  )
}