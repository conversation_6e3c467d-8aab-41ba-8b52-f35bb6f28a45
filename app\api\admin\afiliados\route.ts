import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase } from "@/lib/database-config"
import { getAfiliados, getAfiliadosStats, createAfiliado } from "@/lib/database"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const filters = {
      status: searchParams.get("status") || undefined,
      search: searchParams.get("search") || undefined,
    }

    const [afiliados, stats] = await Promise.all([getAfiliados(filters), getAfiliadosStats()])

    return NextResponse.json(
      {
        afiliados: afiliados || [],
        stats: stats || { total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  } catch (error) {
    console.error("Erro ao buscar afiliados:", error)

    // Se é erro de conexão, retornar dados de fallback
    if (error.code === 'ER_CON_COUNT_ERROR' || error.message?.includes('Too many connections')) {
      console.warn('⚠️ Erro de conexão com banco, retornando dados de fallback')
      return NextResponse.json({
        afiliados: [],
        stats: { total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 },
        message: 'Dados temporariamente indisponíveis'
      })
    }

    return NextResponse.json(
      {
        error: "Erro ao carregar afiliados",
        afiliados: [],
        stats: { total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 },
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { nome, email, telefone, percentual_comissao, senha } = body

    // Validações básicas
    if (!nome || !email || !senha) {
      return NextResponse.json(
        { error: "Nome, email e senha são obrigatórios" },
        { status: 400 }
      )
    }

    if (!email.includes("@")) {
      return NextResponse.json(
        { error: "Email inválido" },
        { status: 400 }
      )
    }

    if (percentual_comissao < 0 || percentual_comissao > 100) {
      return NextResponse.json(
        { error: "Percentual de comissão deve estar entre 0 e 100" },
        { status: 400 }
      )
    }

    const afiliadoId = await createAfiliado({
      nome,
      email,
      telefone: telefone || null,
      percentual_comissao: percentual_comissao || 5,
      senha
    })

    return NextResponse.json(
      { 
        success: true, 
        message: "Afiliado criado com sucesso",
        id: afiliadoId
      },
      { status: 201 }
    )
  } catch (error: any) {
    console.error("Erro ao criar afiliado:", error)
    
    if (error.message.includes("já cadastrado")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
