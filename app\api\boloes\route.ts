import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Buscando bolões reais...')

    // Tentar buscar bolões reais, mas com fallback para dados de teste
    let boloes: any[] = []

    try {
      boloes = await executeQuery(`
        SELECT * FROM boloes
        WHERE status IN ('ativo', 'em_breve')
        ORDER BY id DESC
        LIMIT 10
      `)
      console.log(`📊 Bolões encontrados: ${boloes.length}`)
    } catch (dbError) {
      console.warn('⚠️ Erro ao conectar com banco, usando dados de teste:', dbError.message)

      // Retornar dados de teste imediatamente se houver erro de conexão
      return NextResponse.json({
        success: true,
        boloes: [{
          id: 999,
          nome: 'Bolão Brasil - Teste',
          descricao: 'Bolão de teste com times brasileiros reais',
          valor_aposta: 25.0,
          premio_total: 1000.0,
          data_inicio: new Date().toISOString(),
          data_fim: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'ativo',
          participantes: 0,
          max_participantes: 100,
          total_jogos: 11,
          criador: 'Admin',
          banner_image: '/placeholder.svg?height=200&width=400',
          campeonatos_selecionados: [{ codigo: 'BSA', nome: 'Campeonato Brasileiro Série A' }],
          jogos: [
            {
              id: 2001,
              time_casa: 'Flamengo',
              time_fora: 'Palmeiras',
              time_casa_logo: 'https://crests.football-data.org/1783.png',
              time_fora_logo: 'https://crests.football-data.org/1769.png',
              data_jogo: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2002,
              time_casa: 'Corinthians',
              time_fora: 'São Paulo',
              time_casa_logo: 'https://crests.football-data.org/1779.png',
              time_fora_logo: 'https://crests.football-data.org/1776.png',
              data_jogo: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2003,
              time_casa: 'Santos',
              time_fora: 'Botafogo',
              time_casa_logo: 'https://crests.football-data.org/6685.png',
              time_fora_logo: 'https://crests.football-data.org/1770.png',
              data_jogo: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2004,
              time_casa: 'Vasco da Gama',
              time_fora: 'Fluminense',
              time_casa_logo: 'https://crests.football-data.org/1780.png',
              time_fora_logo: 'https://crests.football-data.org/1765.png',
              data_jogo: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2005,
              time_casa: 'Grêmio',
              time_fora: 'Internacional',
              time_casa_logo: 'https://crests.football-data.org/1767.png',
              time_fora_logo: 'https://crests.football-data.org/6684.png',
              data_jogo: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2006,
              time_casa: 'Atlético Mineiro',
              time_fora: 'Cruzeiro',
              time_casa_logo: 'https://crests.football-data.org/1766.png',
              time_fora_logo: 'https://crests.football-data.org/1771.png',
              data_jogo: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2007,
              time_casa: 'Bahia',
              time_fora: 'Vitória',
              time_casa_logo: 'https://crests.football-data.org/1777.png',
              time_fora_logo: 'https://crests.football-data.org/1782.png',
              data_jogo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2008,
              time_casa: 'Fortaleza',
              time_fora: 'Ceará',
              time_casa_logo: 'https://crests.football-data.org/3984.png',
              time_fora_logo: 'https://crests.football-data.org/1837.png',
              data_jogo: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2009,
              time_casa: 'Bragantino',
              time_fora: 'Juventude',
              time_casa_logo: 'https://crests.football-data.org/4286.png',
              time_fora_logo: 'https://crests.football-data.org/4245_large.png',
              data_jogo: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2010,
              time_casa: 'Sport Recife',
              time_fora: 'Mirassol',
              time_casa_logo: 'https://crests.football-data.org/1778.png',
              time_fora_logo: 'https://crests.football-data.org/4364.png',
              data_jogo: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            },
            {
              id: 2011,
              time_casa: 'Flamengo',
              time_fora: 'Vasco da Gama',
              time_casa_logo: 'https://crests.football-data.org/1783.png',
              time_fora_logo: 'https://crests.football-data.org/1780.png',
              data_jogo: new Date(Date.now() + 11 * 24 * 60 * 60 * 1000).toISOString(),
              campeonato: 'Campeonato Brasileiro Série A',
              campeonato_codigo: 'BSA',
              status: 'agendado'
            }
          ]
        }]
      })
    }

    // Formatar bolões e buscar jogos
    const boloesFormatados = await Promise.all(boloes.map(async (bolao: any) => {
      let campeonatos = []
      let jogos = []

      // Tentar parsear campeonatos selecionados
      if (bolao.campeonatos_selecionados) {
        try {
          campeonatos = JSON.parse(bolao.campeonatos_selecionados)
        } catch (e) {
          console.warn(`⚠️ Erro ao parsear campeonatos do bolão ${bolao.id}`)
        }
      }

      // Buscar jogos do bolão
      try {
        // Primeiro tentar buscar da tabela bolao_jogos
        const jogosAssociados = await executeQuery(`
          SELECT
            j.*,
            tc.nome as time_casa_nome,
            tc.nome_curto as time_casa_curto,
            tc.logo_url as time_casa_logo,
            tf.nome as time_fora_nome,
            tf.nome_curto as time_fora_curto,
            tf.logo_url as time_fora_logo,
            c.nome as campeonato_nome,
            c.codigo as campeonato_codigo
          FROM bolao_jogos bj
          JOIN jogos j ON bj.jogo_id = j.id
          LEFT JOIN times tc ON j.time_casa_id = tc.id
          LEFT JOIN times tf ON j.time_fora_id = tf.id
          LEFT JOIN campeonatos c ON j.campeonato_id = c.id
          WHERE bj.bolao_id = ?
          ORDER BY j.data_jogo ASC
        `, [bolao.id])

        // Se não há jogos associados, tentar buscar das partidas selecionadas
        if (jogosAssociados.length === 0 && bolao.partidas_selecionadas) {
          try {
            const partidasSelecionadas = JSON.parse(bolao.partidas_selecionadas)
            if (partidasSelecionadas && partidasSelecionadas.length > 0) {
              console.log(`🔍 Buscando ${partidasSelecionadas.length} partidas selecionadas para bolão ${bolao.id}`)

              // Buscar jogos das partidas selecionadas
              const partidasIds = partidasSelecionadas.map((p: any) => p.id || p).filter(Boolean)
              if (partidasIds.length > 0) {
                const placeholders = partidasIds.map(() => '?').join(',')
                const jogosPartidas = await executeQuery(`
                  SELECT
                    j.*,
                    tc.nome as time_casa_nome,
                    tc.nome_curto as time_casa_curto,
                    tc.logo_url as time_casa_logo,
                    tf.nome as time_fora_nome,
                    tf.nome_curto as time_fora_curto,
                    tf.logo_url as time_fora_logo,
                    c.nome as campeonato_nome,
                    c.codigo as campeonato_codigo
                  FROM jogos j
                  LEFT JOIN times tc ON j.time_casa_id = tc.id
                  LEFT JOIN times tf ON j.time_fora_id = tf.id
                  LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                  WHERE j.id IN (${placeholders})
                  ORDER BY j.data_jogo ASC
                `, partidasIds)

                jogos = jogosPartidas
              }
            }
          } catch (e) {
            console.warn(`⚠️ Erro ao parsear partidas selecionadas do bolão ${bolao.id}`)
          }
        }
        // Se ainda não há jogos e há campeonatos selecionados, buscar jogos desses campeonatos
        else if (jogosAssociados.length === 0 && campeonatos.length > 0) {
          console.log(`🔍 Buscando jogos de TODOS os campeonatos selecionados para bolão ${bolao.id}`)

          // Buscar jogos de TODOS os campeonatos selecionados (MÁXIMO 11 TOTAL)
          const codigosCampeonatos = campeonatos.map((c: any) => c.codigo).filter(Boolean)
          if (codigosCampeonatos.length > 0) {
            console.log(`🏆 Campeonatos: ${codigosCampeonatos.join(', ')}`)

            // Calcular quantos jogos por campeonato (distribuição equilibrada)
            const jogosPorCampeonato = Math.ceil(11 / codigosCampeonatos.length)
            console.log(`📊 Distribuição: ${jogosPorCampeonato} jogos por campeonato (máx 11 total)`)

            // Buscar jogos de cada campeonato separadamente para garantir representação de todos
            let todosJogos: any[] = []
            for (const codigo of codigosCampeonatos) {
              const jogosCampeonato = await executeQuery(`
                SELECT
                  j.*,
                  tc.nome as time_casa_nome,
                  tc.nome_curto as time_casa_curto,
                  tc.logo_url as time_casa_logo,
                  tf.nome as time_fora_nome,
                  tf.nome_curto as time_fora_curto,
                  tf.logo_url as time_fora_logo,
                  c.nome as campeonato_nome,
                  c.codigo as campeonato_codigo
                FROM jogos j
                LEFT JOIN times tc ON j.time_casa_id = tc.id
                LEFT JOIN times tf ON j.time_fora_id = tf.id
                LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                WHERE c.codigo = ?
                AND j.status IN ('agendado', 'ao_vivo')
                AND j.data_jogo >= NOW()
                ORDER BY j.data_jogo ASC
                LIMIT ?
              `, [codigo, jogosPorCampeonato])

              console.log(`📊 ${codigo}: ${jogosCampeonato.length} jogos encontrados`)
              todosJogos = [...todosJogos, ...jogosCampeonato]
            }

            // Ordenar todos os jogos por data
            jogos = todosJogos.sort((a, b) => new Date(a.data_jogo).getTime() - new Date(b.data_jogo).getTime())

            // Se não temos 11 jogos, buscar mais de qualquer campeonato
            if (jogos.length < 11) {
              console.log(`⚠️ Apenas ${jogos.length} jogos encontrados, buscando mais...`)

              const jogosAdicionais = await executeQuery(`
                SELECT
                  j.*,
                  tc.nome as time_casa_nome,
                  tc.nome_curto as time_casa_curto,
                  tc.logo_url as time_casa_logo,
                  tf.nome as time_fora_nome,
                  tf.nome_curto as time_fora_curto,
                  tf.logo_url as time_fora_logo,
                  c.nome as campeonato_nome,
                  c.codigo as campeonato_codigo
                FROM jogos j
                LEFT JOIN times tc ON j.time_casa_id = tc.id
                LEFT JOIN times tf ON j.time_fora_id = tf.id
                LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                WHERE j.status IN ('agendado', 'ao_vivo')
                AND j.data_jogo >= NOW()
                ORDER BY j.data_jogo ASC
                LIMIT ?
              `, [11])

              // Combinar e remover duplicatas
              const jogosExistentesIds = new Set(jogos.map((j: any) => j.id))
              const novosjogos = jogosAdicionais.filter((j: any) => !jogosExistentesIds.has(j.id))

              jogos = [...jogos, ...novosjogos]
                .sort((a, b) => new Date(a.data_jogo).getTime() - new Date(b.data_jogo).getTime())
                .slice(0, 11)

              console.log(`✅ Total final de jogos: ${jogos.length}`)
            } else {
              // Limitar a 11 se temos mais
              jogos = jogos.slice(0, 11)
              console.log(`✅ Total de jogos carregados: ${jogos.length} (limitado a 11)`)
            }
          }
        } else {
          jogos = jogosAssociados
        }

      } catch (error) {
        console.error(`❌ Erro ao buscar jogos do bolão ${bolao.id}:`, error)
      }

      return {
        id: bolao.id,
        nome: bolao.nome || 'Bolão',
        descricao: bolao.descricao || 'Descrição do bolão',
        valor_aposta: parseFloat(bolao.valor_aposta || 25),
        premio_total: parseFloat(bolao.premio_total || 1000),
        data_inicio: bolao.data_inicio,
        data_fim: bolao.data_fim,
        status: bolao.status,
        participantes: 0,
        max_participantes: bolao.max_participantes || 100,
        total_jogos: jogos.length,
        criador: 'Admin',
        banner_image: bolao.banner_image,
        campeonatos_selecionados: campeonatos,
        jogos: jogos.map((jogo: any) => {
          // Debug: Log dos dados do jogo
          console.log(`🔍 Jogo ${jogo.id}:`, {
            time_casa_nome: jogo.time_casa_nome,
            time_casa: jogo.time_casa,
            time_fora_nome: jogo.time_fora_nome,
            time_fora: jogo.time_fora,
            campeonato_nome: jogo.campeonato_nome,
            campeonato_codigo: jogo.campeonato_codigo
          })

          return {
            id: jogo.id,
            time_casa: jogo.time_casa_nome || jogo.time_casa || "Time Casa",
            time_fora: jogo.time_fora_nome || jogo.time_fora || "Time Fora",
            time_casa_logo: jogo.time_casa_logo || "/placeholder.svg",
            time_fora_logo: jogo.time_fora_logo || "/placeholder.svg",
            data_jogo: jogo.data_jogo,
            campeonato: jogo.campeonato_nome || "Campeonato",
            campeonato_codigo: jogo.campeonato_codigo || "CAMP",
            status: jogo.status,
            resultado_casa: jogo.resultado_casa,
            resultado_fora: jogo.resultado_fora
          }
        })
      }
    }))

    console.log(`✅ Retornando ${boloesFormatados.length} bolões reais`)

    return NextResponse.json({
      success: true,
      boloes: boloesFormatados
    })

  } catch (error) {
    console.error('❌ Erro ao buscar bolões:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      boloes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      jogos_selecionados,
      regras
    } = body

    if (!nome || !valor_aposta || !premio_total || !data_inicio || !data_fim) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Por enquanto, vamos usar um usuário padrão como criador
    // Em produção, isso deveria vir da sessão do usuário
    const criado_por = 1

    // Inserir bolão
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'em_breve', ?, ?)
    `, [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || null,
      min_acertos || 3,
      data_inicio,
      data_fim,
      criado_por,
      JSON.stringify(regras || [])
    ])

    const bolao_id = (result as any).insertId

    // Inserir jogos do bolão se fornecidos
    if (jogos_selecionados && jogos_selecionados.length > 0) {
      for (const jogo_id of jogos_selecionados) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao_id, jogo_id])
      }
    }

    return NextResponse.json({
      success: true,
      bolao_id,
      message: 'Bolão criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar bolão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
