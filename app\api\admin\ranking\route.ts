import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    console.log('🏆 Buscando ranking de usuários...')

    // Buscar ranking dos usuários baseado em apostas e acertos
    const ranking = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.data_cadastro,
        COUNT(DISTINCT a.id) as total_apostas,
        COUNT(DISTINCT CASE WHEN a.status = 'pago' THEN a.id END) as apostas_pagas,
        COALESCE(SUM(a.valor_total), 0) as valor_total_apostado,
        COALESCE(AVG(CASE WHEN a.status = 'pago' THEN a.valor_total END), 0) as valor_medio_aposta,
        COUNT(DISTINCT ad.id) as total_palpites,
        COUNT(DISTINCT CASE WHEN ad.acertou = 1 THEN ad.id END) as palpites_corretos,
        CASE 
          WHEN COUNT(DISTINCT ad.id) > 0 
          THEN ROUND((COUNT(DISTINCT CASE WHEN ad.acertou = 1 THEN ad.id END) * 100.0) / COUNT(DISTINCT ad.id), 2)
          ELSE 0 
        END as percentual_acerto,
        COALESCE(SUM(p.valor_premio), 0) as total_premios
      FROM usuarios u
      LEFT JOIN apostas a ON u.id = a.usuario_id
      LEFT JOIN aposta_detalhes ad ON a.id = ad.aposta_id
      LEFT JOIN premios p ON a.id = p.aposta_id
      WHERE u.tipo = 'usuario' AND u.status = 'ativo'
      GROUP BY u.id, u.nome, u.email, u.data_cadastro
      HAVING total_apostas > 0
      ORDER BY 
        percentual_acerto DESC,
        total_apostas DESC,
        valor_total_apostado DESC
      LIMIT 50
    `)

    // Adicionar posição no ranking
    const rankingWithPosition = ranking.map((user: any, index: number) => ({
      ...user,
      posicao: index + 1,
      total_apostas: parseInt(user.total_apostas) || 0,
      apostas_pagas: parseInt(user.apostas_pagas) || 0,
      valor_total_apostado: parseFloat(user.valor_total_apostado) || 0,
      valor_medio_aposta: parseFloat(user.valor_medio_aposta) || 0,
      total_palpites: parseInt(user.total_palpites) || 0,
      palpites_corretos: parseInt(user.palpites_corretos) || 0,
      percentual_acerto: parseFloat(user.percentual_acerto) || 0,
      total_premios: parseFloat(user.total_premios) || 0
    }))

    // Buscar estatísticas gerais - corrigido para evitar funções de agregação aninhadas
    const statsQuery = await executeQuery(`
      SELECT
        COUNT(DISTINCT u.id) as total_usuarios_ativos,
        COUNT(DISTINCT a.id) as total_apostas_sistema,
        COALESCE(SUM(a.valor_total), 0) as volume_total_apostas
      FROM usuarios u
      LEFT JOIN apostas a ON u.id = a.usuario_id
      WHERE u.tipo = 'usuario' AND u.status = 'ativo'
    `)

    // Buscar taxa de acerto geral separadamente
    const acertoQuery = await executeQuery(`
      SELECT
        CASE
          WHEN COUNT(ad.id) > 0
          THEN ROUND((COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) * 100.0) / COUNT(ad.id), 2)
          ELSE 0
        END as taxa_acerto_geral
      FROM usuarios u
      LEFT JOIN apostas a ON u.id = a.usuario_id
      LEFT JOIN aposta_detalhes ad ON a.id = ad.aposta_id
      WHERE u.tipo = 'usuario' AND u.status = 'ativo'
        AND a.id IS NOT NULL
    `)

    const stats = statsQuery[0] || {
      total_usuarios_ativos: 0,
      total_apostas_sistema: 0,
      volume_total_apostas: 0
    }

    const acertoStats = acertoQuery[0] || {
      taxa_acerto_geral: 0
    }

    return NextResponse.json({
      success: true,
      ranking: rankingWithPosition,
      stats: {
        total_usuarios_ativos: parseInt(stats.total_usuarios_ativos) || 0,
        total_apostas_sistema: parseInt(stats.total_apostas_sistema) || 0,
        volume_total_apostas: parseFloat(stats.volume_total_apostas) || 0,
        taxa_acerto_geral: parseFloat(acertoStats.taxa_acerto_geral) || 0
      }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar ranking:', error)

    // Se é erro de conexão, retornar dados de fallback
    if (error.code === 'ER_CON_COUNT_ERROR' || error.message?.includes('Too many connections')) {
      console.warn('⚠️ Erro de conexão com banco, retornando dados de fallback')
      return NextResponse.json({
        success: true,
        ranking: [],
        stats: {
          total_usuarios_ativos: 0,
          total_apostas_sistema: 0,
          volume_total_apostas: 0,
          taxa_acerto_geral: 0
        },
        message: 'Dados temporariamente indisponíveis'
      })
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Erro ao carregar ranking',
        ranking: [],
        stats: {
          total_usuarios_ativos: 0,
          total_apostas_sistema: 0,
          volume_total_apostas: 0,
          taxa_acerto_geral: 0
        }
      },
      { status: 500 }
    )
  }
}
