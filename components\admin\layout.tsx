"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { AdminSidebar } from "./sidebar"
import { cn } from "@/lib/utils"
import { Toaster } from "@/components/ui/sonner"

interface AdminLayoutProps {
  children: React.ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAuth = () => {
      // Skip auth check for login page
      if (pathname === "/admin/login") {
        setIsLoading(false)
        return
      }

      const admin = localStorage.getItem("admin")
      if (!admin) {
        router.push("/admin/login")
        return
      }

      try {
        const adminData = JSON.parse(admin)
        if (adminData.tipo !== "admin") {
          router.push("/admin/login")
          return
        }
        setIsAuthenticated(true)
      } catch (error) {
        router.push("/admin/login")
        return
      }

      setIsLoading(false)
    }

    checkAuth()
  }, [router, pathname])

  // Don't render layout for login page
  if (pathname === "/admin/login") {
    return (
      <>
        {children}
        <Toaster />
      </>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-slate-50 overflow-x-hidden">
      <AdminSidebar collapsed={sidebarCollapsed} onCollapsedChange={setSidebarCollapsed} />
      <main className={cn(
        "transition-all duration-300",
        "ml-0 lg:ml-64", // Mobile: no margin, Desktop: sidebar margin
        sidebarCollapsed && "lg:ml-16" // Collapsed sidebar margin
      )}>
        <div className="p-3 sm:p-4 md:p-6 lg:p-8 pt-16 lg:pt-6">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </div>
      </main>
      <Toaster
        position="top-right"
        toastOptions={{
          className: "text-sm",
          duration: 4000,
        }}
      />
    </div>
  )
}
