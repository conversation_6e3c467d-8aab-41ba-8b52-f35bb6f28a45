#!/usr/bin/env node

/**
 * Script para adicionar o tipo 'gerente' à tabela de usuários
 */

import mysql from 'mysql2/promise'

async function addGerenteType() {
  let connection

  try {
    console.log('🔧 Conectando ao banco de dados...')
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      connectTimeout: 10000
    })

    console.log('✅ Conectado ao banco de dados')

    // Verificar se o tipo 'gerente' já existe
    console.log('🔍 Verificando tipos existentes...')
    
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM usuarios LIKE 'tipo'
    `)

    if (columns.length > 0) {
      const typeColumn = columns[0]
      console.log('📋 Coluna tipo atual:', typeColumn.Type)

      // Verificar se 'gerente' já está incluído
      if (typeColumn.Type.includes('gerente')) {
        console.log('✅ Tipo "gerente" já existe na tabela')
        return
      }

      // Adicionar 'gerente' aos tipos existentes
      console.log('🔧 Adicionando tipo "gerente"...')
      
      await connection.execute(`
        ALTER TABLE usuarios 
        MODIFY COLUMN tipo ENUM('admin', 'usuario', 'cambista', 'gerente') DEFAULT 'usuario'
      `)

      console.log('✅ Tipo "gerente" adicionado com sucesso!')

      // Verificar a alteração
      const [newColumns] = await connection.execute(`
        SHOW COLUMNS FROM usuarios LIKE 'tipo'
      `)
      
      if (newColumns.length > 0) {
        console.log('📋 Nova definição da coluna:', newColumns[0].Type)
      }

    } else {
      console.log('❌ Coluna "tipo" não encontrada na tabela usuarios')
    }

  } catch (error) {
    console.error('❌ Erro:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Dica: Verifique se o MySQL está rodando')
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 Dica: Verifique se o banco "sistema-bolao-top" existe')
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Conexão fechada')
    }
  }
}

// Executar o script
addGerenteType()
