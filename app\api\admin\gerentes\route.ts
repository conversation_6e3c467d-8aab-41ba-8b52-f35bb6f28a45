import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"
import bcrypt from "bcryptjs"

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const filters = {
      status: searchParams.get("status") || undefined,
      search: searchParams.get("search") || undefined,
    }

    console.log("🔍 Buscando gerentes com filtros:", filters)

    // Buscar gerentes (usuários com tipo 'gerente')
    let query = `
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.status,
        u.data_cadastro,
        u.data_atualizacao,
        COUNT(DISTINCT sub.id) as total_usuarios,
        COALESCE(SUM(b.valor_total), 0) as comissao_total
      FROM usuarios u
      LEFT JOIN usuarios sub ON sub.afiliado_id = u.id
      LEFT JOIN bilhetes b ON b.usuario_id = sub.id AND b.status = 'pago'
      WHERE u.tipo = 'gerente'
    `

    const params: any[] = []

    if (filters.status) {
      query += ` AND u.status = ?`
      params.push(filters.status)
    }

    if (filters.search) {
      query += ` AND (u.nome LIKE ? OR u.email LIKE ?)`
      params.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    query += ` GROUP BY u.id ORDER BY u.data_cadastro DESC`

    const gerentes = await executeQuery(query, params)

    // Buscar estatísticas
    const statsQuery = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'ativo' THEN 1 ELSE 0 END) as ativos,
        SUM(CASE WHEN status = 'inativo' THEN 1 ELSE 0 END) as inativos
      FROM usuarios 
      WHERE tipo = 'gerente'
    `

    const statsResult = await executeQuery(statsQuery)
    const stats = statsResult[0] || { total: 0, ativos: 0, inativos: 0 }

    console.log(`✅ Encontrados ${gerentes.length} gerentes`)

    return NextResponse.json({
      gerentes: gerentes || [],
      stats: stats,
    })
  } catch (error) {
    console.error("Erro ao buscar gerentes:", error)
    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        gerentes: [],
        stats: { total: 0, ativos: 0, inativos: 0 },
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { nome, email, telefone, nivel_acesso, senha } = body

    console.log("➕ Criando novo gerente:", { nome, email, nivel_acesso })

    // Validações básicas
    if (!nome || !email || !senha) {
      return NextResponse.json(
        { error: "Nome, email e senha são obrigatórios" },
        { status: 400 }
      )
    }

    if (!email.includes("@")) {
      return NextResponse.json(
        { error: "Email inválido" },
        { status: 400 }
      )
    }

    if (senha.length < 6) {
      return NextResponse.json(
        { error: "Senha deve ter pelo menos 6 caracteres" },
        { status: 400 }
      )
    }

    // Verificar se email já existe
    const existingUser = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE email = ?",
      [email]
    )

    if (existingUser) {
      return NextResponse.json(
        { error: "Email já cadastrado no sistema" },
        { status: 400 }
      )
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 10)

    // Inserir gerente
    const result = await executeQuery(
      `INSERT INTO usuarios (nome, email, telefone, senha_hash, tipo, status, data_cadastro)
       VALUES (?, ?, ?, ?, 'gerente', 'ativo', NOW())`,
      [nome, email, telefone || null, senhaHash]
    )

    const gerenteId = (result as any).insertId

    console.log("✅ Gerente criado com ID:", gerenteId)

    return NextResponse.json({
      success: true,
      message: "Gerente criado com sucesso",
      id: gerenteId,
    })
  } catch (error) {
    console.error("Erro ao criar gerente:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { id, nome, email, telefone, status, senha } = body

    console.log("✏️ Atualizando gerente:", { id, nome, email, status })

    // Validações básicas
    if (!id || !nome || !email) {
      return NextResponse.json(
        { error: "ID, nome e email são obrigatórios" },
        { status: 400 }
      )
    }

    // Verificar se gerente existe
    const existingGerente = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE id = ? AND tipo = 'gerente'",
      [id]
    )

    if (!existingGerente) {
      return NextResponse.json(
        { error: "Gerente não encontrado" },
        { status: 404 }
      )
    }

    // Preparar query de atualização
    let updateQuery = `
      UPDATE usuarios SET
        nome = ?,
        email = ?,
        telefone = ?,
        status = ?,
        data_atualizacao = NOW()
      WHERE id = ?
    `
    let updateParams = [nome, email, telefone || null, status || 'ativo', id]

    // Se senha foi fornecida, incluir na atualização
    if (senha && senha.trim() !== '') {
      const senhaHash = await bcrypt.hash(senha, 10)
      updateQuery = `
        UPDATE usuarios SET
          nome = ?,
          email = ?,
          telefone = ?,
          status = ?,
          senha_hash = ?,
          data_atualizacao = NOW()
        WHERE id = ?
      `
      updateParams = [nome, email, telefone || null, status || 'ativo', senhaHash, id]
    }

    await executeQuery(updateQuery, updateParams)

    console.log("✅ Gerente atualizado com sucesso")

    return NextResponse.json({
      success: true,
      message: "Gerente atualizado com sucesso",
    })
  } catch (error) {
    console.error("Erro ao atualizar gerente:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "ID é obrigatório" },
        { status: 400 }
      )
    }

    console.log("🗑️ Removendo gerente:", id)

    // Verificar se gerente existe
    const existingGerente = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE id = ? AND tipo = 'gerente'",
      [id]
    )

    if (!existingGerente) {
      return NextResponse.json(
        { error: "Gerente não encontrado" },
        { status: 404 }
      )
    }

    // Remover gerente
    await executeQuery("DELETE FROM usuarios WHERE id = ?", [id])

    console.log("✅ Gerente removido com sucesso")

    return NextResponse.json({
      success: true,
      message: "Gerente removido com sucesso",
    })
  } catch (error) {
    console.error("Erro ao remover gerente:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
