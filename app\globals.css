@tailwind base;
@tailwind components;
@tailwind utilities;

/* Importar tema escuro personalizado para modais */
@import '../styles/modal-dark-theme.css';

/* Mobile optimizations */
@layer base {
  html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    touch-action: manipulation;
  }

  /* Prevent horizontal scroll */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Better touch targets */
  button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  @media (max-width: 640px) {
    body {
      font-size: 16px; /* Prevent zoom on iOS */
    }
  }
}

@layer utilities {
  /* Safe area for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Better scrolling on mobile */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent text selection on UI elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Mobile-specific improvements */
  .mobile-friendly {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Enhanced responsive breakpoints */
  .container-responsive {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-responsive {
      max-width: 640px;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .container-responsive {
      max-width: 768px;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .container-responsive {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .container-responsive {
      max-width: 1280px;
    }
  }

  /* Improved text scaling for different devices */
  .text-responsive {
    font-size: 0.875rem; /* 14px */
    line-height: 1.25rem; /* 20px */
  }

  @media (min-width: 768px) {
    .text-responsive {
      font-size: 1rem; /* 16px */
      line-height: 1.5rem; /* 24px */
    }
  }

  @media (min-width: 1024px) {
    .text-responsive {
      font-size: 1.125rem; /* 18px */
      line-height: 1.75rem; /* 28px */
    }
  }

  /* Responsive tables */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-responsive table {
    min-width: 600px;
  }

  /* iOS specific optimizations */
  @supports (-webkit-touch-callout: none) {
    .ios-optimized {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -webkit-tap-highlight-color: transparent;
    }
  }

  /* Android specific optimizations */
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    .android-optimized {
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      -webkit-focus-ring-color: rgba(0, 0, 0, 0);
    }
  }

  /* Better button interactions for touch devices */
  .touch-button {
    min-height: 44px; /* iOS recommended minimum touch target */
    min-width: 44px;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  @media (hover: hover) and (pointer: fine) {
    .touch-button:hover {
      transform: translateY(-1px);
    }
  }

  /* Improved close buttons */
  .close-button {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  @media (max-width: 640px) {
    .close-button {
      min-height: 48px;
      min-width: 48px;
    }
  }

  /* Better modal positioning on mobile */
  @media (max-width: 640px) {
    [role="dialog"] {
      margin: 8px;
      max-height: calc(100vh - 16px);
      width: calc(100vw - 16px);
      max-width: none;
    }
  }

  /* Improved scrolling for mobile */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  /* Better focus states for accessibility */
  .focus-visible:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Responsive grid improvements */
  .grid-responsive {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: 1fr;
  }

  @media (min-width: 640px) {
    .grid-responsive {
      gap: 1rem;
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 768px) {
    .grid-responsive {
      gap: 1.5rem;
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive {
      gap: 2rem;
    }
  }

  /* Safe area support for devices with notch */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Viewport height fix for mobile browsers */
  .min-h-screen-mobile {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  /* Smooth scrolling for all devices */
  html {
    scroll-behavior: smooth;
  }

  /* Better font rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Better mobile inputs */
  @media (max-width: 640px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    textarea,
    select {
      font-size: 16px !important; /* Prevent zoom on iOS */
    }
  }

  /* Overlay cinza para todos os modais */
  [data-radix-dialog-overlay] {
    background-color: rgba(17, 24, 39, 0.75) !important; /* bg-gray-900/75 */
    backdrop-filter: blur(4px) !important;
    -webkit-backdrop-filter: blur(4px) !important;
  }

  /* Modal responsivo e centralizado para todas as telas */
  [data-radix-dialog-content] {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    background-color: #545454 !important; /* Fundo cinza escuro personalizado */
    border: 1px solid #666666 !important; /* Borda mais escura */
    border-radius: 0.5rem !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4) !important;
    color: white !important; /* Texto branco para contraste */
  }

  /* Responsividade específica para modais */
  @media (max-width: 640px) {
    [data-radix-dialog-content] {
      width: 95vw !important;
      max-width: 95vw !important;
      max-height: 95vh !important;
      padding: 1rem !important;
      border-radius: 0.75rem !important; /* Bordas mais arredondadas no mobile */
      margin: 0.5rem !important; /* Pequena margem para evitar tocar as bordas */
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    [data-radix-dialog-content] {
      width: 90vw !important;
      max-width: 36rem !important; /* max-w-xl */
      max-height: 85vh !important;
      padding: 1.5rem !important;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    [data-radix-dialog-content] {
      width: 80vw !important;
      max-width: 42rem !important; /* max-w-2xl */
      max-height: 80vh !important;
      padding: 1.5rem !important;
    }
  }

  @media (min-width: 1025px) and (max-width: 1280px) {
    [data-radix-dialog-content] {
      width: 70vw !important;
      max-width: 48rem !important; /* max-w-3xl */
      max-height: 80vh !important;
      padding: 1.5rem !important;
    }
  }

  @media (min-width: 1281px) {
    [data-radix-dialog-content] {
      width: 60vw !important;
      max-width: 56rem !important; /* max-w-4xl */
      max-height: 80vh !important;
      padding: 2rem !important;
    }
  }

  /* Botão de fechar responsivo */
  [data-radix-dialog-content] button[aria-label="Close"] {
    position: absolute !important;
    top: 0.75rem !important;
    right: 0.75rem !important;
    width: 2.25rem !important; /* 36px */
    height: 2.25rem !important; /* 36px */
    border-radius: 50% !important;
    background-color: white !important;
    border: 1px solid rgb(209 213 219) !important; /* border-gray-300 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 20 !important;
    transition: all 0.2s ease !important;
  }

  [data-radix-dialog-content] button[aria-label="Close"]:hover {
    background-color: rgb(243 244 246) !important; /* bg-gray-100 */
  }

  @media (min-width: 640px) {
    [data-radix-dialog-content] button[aria-label="Close"] {
      width: 2.5rem !important; /* 40px */
      height: 2.5rem !important; /* 40px */
      top: 1rem !important;
      right: 1rem !important;
    }
  }

  /* Estilos para títulos e conteúdo dos modais */
  [data-radix-dialog-content] h2,
  [data-radix-dialog-content] .dialog-title {
    font-size: 1.25rem !important; /* text-xl */
    font-weight: 600 !important; /* font-semibold */
    color: white !important; /* Texto branco para contraste */
    margin-bottom: 0.5rem !important;
    line-height: 1.4 !important;
  }

  [data-radix-dialog-content] .dialog-description {
    font-size: 0.875rem !important; /* text-sm */
    color: rgb(200 200 200) !important; /* Texto cinza claro */
    margin-bottom: 1rem !important;
  }

  /* Responsividade para títulos */
  @media (max-width: 640px) {
    [data-radix-dialog-content] h2,
    [data-radix-dialog-content] .dialog-title {
      font-size: 1.125rem !important; /* text-lg */
      margin-bottom: 0.75rem !important;
    }
  }

  /* Estilos para listas dentro dos modais */
  [data-radix-dialog-content] .payment-item,
  [data-radix-dialog-content] .ticket-item {
    padding: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    background-color: rgba(255, 255, 255, 0.1) !important; /* Fundo semi-transparente */
    border: 1px solid rgba(255, 255, 255, 0.2) !important; /* Borda semi-transparente */
    border-radius: 0.5rem !important;
    border-left: 4px solid rgb(34 197 94) !important; /* border-l-green-500 */
    color: white !important; /* Texto branco */
  }

  [data-radix-dialog-content] .payment-item.pending,
  [data-radix-dialog-content] .ticket-item.pending {
    border-left-color: rgb(251 191 36) !important; /* border-l-yellow-400 */
  }

  /* Scroll suave para modais */
  [data-radix-dialog-content] {
    scroll-behavior: smooth !important;
  }

  [data-radix-dialog-content]::-webkit-scrollbar {
    width: 6px !important;
  }

  [data-radix-dialog-content]::-webkit-scrollbar-track {
    background: rgb(243 244 246) !important; /* bg-gray-100 */
    border-radius: 3px !important;
  }

  [data-radix-dialog-content]::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3) !important; /* Scrollbar mais claro */
    border-radius: 3px !important;
  }

  /* Estilos adicionais para elementos dentro dos modais */
  [data-radix-dialog-content] p,
  [data-radix-dialog-content] span,
  [data-radix-dialog-content] div {
    color: white !important;
  }

  /* Estilos para cards dentro dos modais */
  [data-radix-dialog-content] .card,
  [data-radix-dialog-content] [class*="card"] {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
  }

  /* Estilos para badges e status */
  [data-radix-dialog-content] .badge,
  [data-radix-dialog-content] [class*="badge"] {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  /* Estilos para inputs dentro dos modais */
  [data-radix-dialog-content] input,
  [data-radix-dialog-content] textarea {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
  }

  [data-radix-dialog-content] input::placeholder,
  [data-radix-dialog-content] textarea::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  /* Estilos específicos para dispositivos móveis */
  @media (max-width: 480px) {
    [data-radix-dialog-content] {
      width: 98vw !important;
      max-width: 98vw !important;
      max-height: 98vh !important;
      padding: 0.75rem !important;
      border-radius: 1rem !important;
      margin: 0.25rem !important;
    }

    /* Títulos menores em telas muito pequenas */
    [data-radix-dialog-content] h2,
    [data-radix-dialog-content] .dialog-title {
      font-size: 1.125rem !important; /* text-lg */
      margin-bottom: 0.75rem !important;
    }

    /* Descrições menores */
    [data-radix-dialog-content] .dialog-description {
      font-size: 0.8rem !important;
      margin-bottom: 0.75rem !important;
    }

    /* Botão de fechar maior para touch */
    [data-radix-dialog-content] button[aria-label="Close"] {
      width: 2.5rem !important;
      height: 2.5rem !important;
      top: 0.5rem !important;
      right: 0.5rem !important;
    }
  }

  /* Estilos específicos para iOS (Safari) */
  @supports (-webkit-touch-callout: none) {
    [data-radix-dialog-content] {
      -webkit-overflow-scrolling: touch !important;
    }
  }

  /* Estilos específicos para Android */
  @media screen and (max-width: 640px) and (orientation: portrait) {
    [data-radix-dialog-content] {
      max-height: 92vh !important; /* Deixar espaço para a barra de navegação do Android */
    }
  }

  [data-radix-dialog-content]::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128) !important; /* bg-gray-500 */
  }
}

/* Suprimir warning do webkit-text-size-adjust */
@layer base {
  html {
    -webkit-text-size-adjust: 100%;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animações customizadas */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
  opacity: 0;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-500 {
  animation-delay: 500ms;
}

.delay-1000 {
  animation-delay: 1000ms;
}

/* Scroll suave */
html {
  scroll-behavior: smooth;
}

/* Responsividade para o carrossel */
@media (max-width: 640px) {
  .carousel-title {
    font-size: 2rem;
  }

  .carousel-subtitle {
    font-size: 1.5rem;
  }

  .carousel-description {
    font-size: 1rem;
  }
}

/* Melhorias para imagens do banner */
.banner-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform;
}

/* Remover pontos/artefatos visuais */
.banner-container {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* Melhorar qualidade do texto */
.banner-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "liga" 1, "kern" 1;
}

/* Sombra de texto melhorada */
.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.8), 0 2px 4px rgba(0, 0, 0, 0.6);
}

.text-shadow-md {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5);
}
