"use client"

import { useState, useEffect } from "react"
import "../styles/payment-success.css"
import "../styles/modal-dark-theme.css"
import { showPaymentSuccessAlert } from "@/lib/sweetalert"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { QRCodeComponent } from "@/components/ui/qr-code"
import TeamLogo from "@/components/TeamLogo"
import CompetitionEmblem from "@/components/CompetitionEmblem"
import {
  ChevronLeft,
  ChevronRight,
  Loader2,
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  Phone,
  Printer,
  CheckCircle,
  XCircle,
  LogOut,
  Receipt,
  CreditCard,
  ChevronDown,
  Calendar,
  DollarSign,
  UserPlus,
} from "lucide-react"
import { formatCPF, validateCPF, getCPFErrorMessage } from "@/lib/cpf-utils"
import { toast } from "sonner"
import { usePixAutoCheck } from "@/hooks/usePixAutoCheck"



interface Team {
  id: number
  name: string
  shortName: string
  crest: string
}

interface Match {
  id: number
  competition: string
  competitionName?: string
  homeTeam: Team
  awayTeam: Team
  utcDate: string
  status: string
}

interface Aposta {
  match_id: number
  resultado: "casa" | "empate" | "fora" | null
}

interface BannerSlide {
  id: number
  title: string
  subtitle: string
  image: string
  color: string
  active: boolean
  order: number
}

interface LoginForm {
  email: string
  senha: string
}

interface RegisterForm {
  nome: string
  email: string
  telefone: string
  cpf: string
  senha: string
  confirmarSenha: string
}

interface PixResponse {
  qr_code_value: string
  qrcode_image: string
  expiration_datetime: string
  status: string
  transaction_id: string
  order_id: string
}

interface BilheteAposta {
  id: string
  codigo?: string
  data: string
  hora: string
  apostas: Array<{
    jogo: string
    resultado: string
  }>
  valor: number
  valor_total?: number
  status: "pendente" | "ganhou" | "perdeu" | "pago"
  qr_code_pix?: string
  transaction_id?: string
}

interface UserData {
  id: number
  nome: string
  email: string
  telefone?: string
  cpf?: string
  tipo: string
}

interface Pagamento {
  id: string
  data: string
  valor: number
  status: "pendente" | "aprovado" | "rejeitado"
  metodo: string
  bilhete_id: string
}

export default function BolaoPage() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [apostas, setApostas] = useState<Aposta[]>([])
  const [matches, setMatches] = useState<Match[]>([])
  const [banners, setBanners] = useState<BannerSlide[]>([])
  const [loading, setLoading] = useState(true)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [showRegisterDialog, setShowRegisterDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showBilheteDialog, setShowBilheteDialog] = useState(false)
  const [apostasEncerradas, setApostasEncerradas] = useState(false)
  const [bolaoFinalizado, setBolaoFinalizado] = useState(false)
  const [ranking, setRanking] = useState<any[]>([])
  const [bolaoAtual, setBolaoAtual] = useState<any>(null)
  const [showUserBilhetes, setShowUserBilhetes] = useState(false)
  const [showUserPagamentos, setShowUserPagamentos] = useState(false)
  const [showUserProfile, setShowUserProfile] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false)
  const [paymentSuccessData, setPaymentSuccessData] = useState<any>(null)
  const [loginLoading, setLoginLoading] = useState(false)
  const [registerLoading, setRegisterLoading] = useState(false)
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [printingBilhete, setPrintingBilhete] = useState(false)
  const [error, setError] = useState("")
  const [cpfError, setCpfError] = useState("")
  const [pixData, setPixData] = useState<PixResponse | null>(null)
  const [pixCodeCopied, setPixCodeCopied] = useState(false)
  const [bilhete, setBilhete] = useState<BilheteAposta | null>(null)
  const [selectedBilhete, setSelectedBilhete] = useState<BilheteAposta | null>(null)
  const [user, setUser] = useState<UserData | null>(null)
  const [userBilhetes, setUserBilhetes] = useState<BilheteAposta[]>([])
  const [userPagamentos, setUserPagamentos] = useState<Pagamento[]>([])
  const [userStats, setUserStats] = useState({
    totalApostas: 0,
    totalGasto: 0,
    totalGanho: 0,
    boloesParticipados: 0,
    taxaAcerto: 0,
    melhorPremio: 0,
  })

  const [loginForm, setLoginForm] = useState<LoginForm>({
    email: "",
    senha: "",
  })

  const [registerForm, setRegisterForm] = useState<RegisterForm>({
    nome: "",
    email: "",
    telefone: "",
    cpf: "",
    senha: "",
    confirmarSenha: "",
  })

  const [codigoAfiliado, setCodigoAfiliado] = useState<string | null>(null)

  // Hook para verificação automática de PIX
  usePixAutoCheck()
  const [valorAposta, setValorAposta] = useState<number>(25.0) // Valor padrão

  // Escutar evento de pagamento confirmado
  useEffect(() => {
    const handlePixPaymentConfirmed = (event: CustomEvent) => {
      console.log('🎉 Pagamento PIX confirmado automaticamente!', event.detail)

      // Mostrar modal de sucesso
      setPaymentSuccessData({
        codigo: event.detail.codigo,
        valor: event.detail.valor,
        data: event.detail.data,
        transactionId: event.detail.transactionId
      })
      setShowPaymentSuccess(true)

      // Fechar modal de pagamento se estiver aberto
      setShowPaymentDialog(false)

      // Tocar som de sucesso
      playSuccessSound()

      // Notificação de sucesso
      toast.success("🎉 PAGO COM SUCESSO! Bilhete aprovado.", {
        duration: 8000,
        style: {
          background: '#10B981',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          padding: '16px',
          borderRadius: '12px',
          boxShadow: '0 10px 25px rgba(16, 185, 129, 0.3)'
        }
      })

      // Mostrar SweetAlert2 de sucesso
      showPaymentSuccessAlert({
        codigo: event.detail.codigo,
        valor: event.detail.valor,
        clientName: user?.nome,
        transactionId: event.detail.transactionId
      })

      // Recarregar bilhetes
      if (user?.id) {
        loadUserData(user.id)
      }
    }

    // Adicionar listener
    window.addEventListener('pixPaymentConfirmed', handlePixPaymentConfirmed as EventListener)

    // Cleanup
    return () => {
      window.removeEventListener('pixPaymentConfirmed', handlePixPaymentConfirmed as EventListener)
    }
  }, [user])

  // Função para tocar som de sucesso
  const playSuccessSound = () => {
    try {
      // Criar um som de sucesso usando Web Audio API
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // Criar uma sequência de notas para um som de sucesso
      const playNote = (frequency: number, duration: number, delay: number = 0) => {
        setTimeout(() => {
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()

          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)

          oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
          oscillator.type = 'sine'

          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration)

          oscillator.start(audioContext.currentTime)
          oscillator.stop(audioContext.currentTime + duration)
        }, delay)
      }

      // Tocar uma melodia de sucesso (Dó - Mi - Sol - Dó)
      playNote(523.25, 0.2, 0)    // Dó
      playNote(659.25, 0.2, 200)  // Mi
      playNote(783.99, 0.2, 400)  // Sol
      playNote(1046.50, 0.4, 600) // Dó oitava

    } catch (error) {
      console.log("Som não disponível:", error)
    }
  }



  useEffect(() => {
    const initializeApp = async () => {
      await loadData()
      await checkUserLogin()
      checkAffiliateCode()
    }
    initializeApp()
  }, [])

  // Listener para atualizações automáticas de PIX
  useEffect(() => {
    const handlePixStatusUpdate = (event: CustomEvent) => {
      console.log('🎉 PIX Status atualizado:', event.detail)

      // Recarregar bilhetes do usuário se estiver logado
      if (user?.id) {
        loadUserData(user.id)
        toast.success(`${event.detail.updated} pagamento(s) confirmado(s)!`)
      }
    }

    window.addEventListener('pixStatusUpdated', handlePixStatusUpdate as EventListener)

    return () => {
      window.removeEventListener('pixStatusUpdated', handlePixStatusUpdate as EventListener)
    }
  }, [user?.id])

  const checkAffiliateCode = () => {
    try {
      const urlParams = new URLSearchParams(window.location.search)
      const refCode = urlParams.get('ref')

      if (refCode) {
        console.log("🔗 Código de afiliado detectado:", refCode)
        setCodigoAfiliado(refCode)

        // Armazenar no localStorage temporariamente
        localStorage.setItem('affiliate_ref', refCode)

        // Mostrar toast informativo
        toast.success(`Você está se registrando através do link de um afiliado!`)
      }
    } catch (error) {
      console.error("❌ Erro ao verificar código de afiliado:", error)
    }
  }

  const checkUserLogin = async () => {
    try {
      const userData = localStorage.getItem("user")
      if (userData) {
        const parsedUser = JSON.parse(userData)
        console.log("✅ Usuário logado:", parsedUser)

        // Verificar se o usuário existe no banco
        try {
          const response = await fetch('/api/admin/usuarios')
          const data = await response.json()
          const userExists = data.usuarios.find((u: any) => u.id === parsedUser.id)

          if (!userExists) {
            console.log("❌ Usuário não existe no banco, usando usuário de teste")
            // Usar o primeiro usuário disponível como fallback
            const testUser = data.usuarios.find((u: any) => u.tipo === 'usuario') || data.usuarios[0]
            if (testUser) {
              const correctedUser = {
                id: testUser.id,
                nome: testUser.nome,
                email: testUser.email,
                cpf: testUser.cpf_cnpj,
                tipo: testUser.tipo
              }
              localStorage.setItem("user", JSON.stringify(correctedUser))
              setUser(correctedUser)
              await loadUserData(correctedUser.id)
              return
            }
          }
        } catch (apiError) {
          console.log("⚠️ Erro ao verificar usuário no banco:", apiError)
        }

        setUser(parsedUser)
        await loadUserData(parsedUser.id)
      } else {
        console.log("❌ Nenhum usuário logado")
      }
    } catch (error) {
      console.error("❌ Erro ao verificar login:", error)
      localStorage.removeItem("user")
    }
  }

  const loadUserData = async (userId: number) => {
    try {
      console.log("🔄 Carregando dados reais do usuário:", userId)

      // Buscar bilhetes reais do banco
      const bilhetesResponse = await fetch(`/api/user/bilhetes?user_id=${userId}`)
      const bilhetesData = await bilhetesResponse.json()

      // Buscar pagamentos reais do banco
      const pagamentosResponse = await fetch(`/api/user/pagamentos?user_id=${userId}`)
      const pagamentosData = await pagamentosResponse.json()

      const bilhetes = bilhetesData.bilhetes || []
      const pagamentos = pagamentosData.pagamentos || []

      // Calcular estatísticas reais
      const totalGasto = pagamentos.reduce((sum: number, p: any) => sum + p.valor, 0)
      const bilhetesGanhos = bilhetes.filter((b: any) => b.status === "ganhou")
      const totalGanho = bilhetesGanhos.reduce((sum: number, b: any) => sum + (b.premio || 0), 0)

      setUserStats({
        totalApostas: bilhetes.length,
        totalGasto: totalGasto,
        totalGanho: totalGanho,
        boloesParticipados: bilhetes.length,
        taxaAcerto:
          bilhetes.length > 0
            ? (bilhetesGanhos.length / bilhetes.length) * 100
            : 0,
        melhorPremio: bilhetesGanhos.length > 0 ? Math.max(...bilhetesGanhos.map((b: any) => b.premio || 0)) : 0,
      })

      setUserBilhetes(bilhetes)
      setUserPagamentos(pagamentos)

      console.log("✅ Dados reais do usuário carregados:", {
        bilhetes: bilhetes.length,
        pagamentos: pagamentos.length,
        stats: { totalApostas: bilhetes.length, totalGasto, totalGanho },
      })
    } catch (error) {
      console.error("❌ Erro ao carregar dados do usuário:", error)
      console.error("❌ Stack trace:", error instanceof Error ? error.stack : 'Unknown error')
      // Em caso de erro, manter arrays vazios
      setUserBilhetes([])
      setUserPagamentos([])
      setUserStats({
        totalApostas: 0,
        totalGasto: 0,
        totalGanho: 0,
        boloesParticipados: 0,
        taxaAcerto: 0,
        melhorPremio: 0,
      })
    }
  }

  const checkBolaoStatus = async (bolaoId: number) => {
    try {
      const response = await fetch(`/api/boloes/${bolaoId}/status`)
      const data = await response.json()

      if (data.success) {
        setApostasEncerradas(data.apostas_encerradas)
        setBolaoFinalizado(data.bolao_finalizado)

        if (data.bolao_finalizado && data.ranking) {
          setRanking(data.ranking)
        }

        console.log("📊 Status do bolão:", {
          apostasEncerradas: data.apostas_encerradas,
          bolaoFinalizado: data.bolao_finalizado,
          ranking: data.ranking?.length || 0
        })
      }
    } catch (error) {
      console.error("❌ Erro ao verificar status do bolão:", error)
    }
  }

  const loadData = async () => {
    try {
      setLoading(true)
      console.log("🔄 Carregando dados da aplicação...")

      // Buscar apenas bolões ativos (que já incluem os jogos selecionados)
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 segundos timeout

      const boloesResponse = await fetch("/api/boloes", {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!boloesResponse.ok) {
        throw new Error(`HTTP error! status: ${boloesResponse.status}`)
      }

      const boloesData = await boloesResponse.json()

      if (boloesData.success && boloesData.boloes.length > 0) {
        // Usar dados reais dos bolões
        const bolaoAtivo = boloesData.boloes[0] // Pegar o primeiro bolão ativo

        // Definir o valor da aposta do bolão ativo
        setValorAposta(parseFloat(bolaoAtivo.valor_aposta || 25.0))

        const adminBanners: BannerSlide[] = [
          {
            id: bolaoAtivo.id,
            title: "BOLÃO BRASIL",
            subtitle: "Faça suas apostas e concorra a prêmios incríveis!",
            image: bolaoAtivo.banner_image || "/placeholder.svg?height=200&width=400",
            color: "from-green-600 to-blue-600",
            active: true,
            order: 1,
          }
        ]

        // Usar apenas os jogos que foram selecionados para este bolão
        const jogosFormatados = (bolaoAtivo.jogos || []).map((jogo: any) => {
          // Debug: verificar dados do jogo
          console.log('🔍 Dados do jogo recebido:', {
            id: jogo.id,
            time_casa: jogo.time_casa,
            time_fora: jogo.time_fora,
            campeonato: jogo.campeonato,
            campeonato_codigo: jogo.campeonato_codigo
          })

          return {
            id: jogo.id,
            competition: (jogo.campeonato_codigo || jogo.campeonato || jogo.campeonato_nome)?.toLowerCase().replace(/\s+/g, '-') || 'campeonato',
            competitionName: jogo.campeonato || jogo.campeonato_nome || 'Campeonato',
            homeTeam: {
              id: jogo.time_casa_id,
              name: jogo.time_casa || jogo.time_casa_nome || "Time Casa",
              shortName: jogo.time_casa_curto || "TC",
              crest: jogo.time_casa_logo || "/placeholder.svg"
            },
            awayTeam: {
              id: jogo.time_fora_id,
              name: jogo.time_fora || jogo.time_fora_nome || "Time Fora",
              shortName: jogo.time_fora_curto || "TF",
              crest: jogo.time_fora_logo || "/placeholder.svg"
            },
            utcDate: jogo.data_jogo,
            status: jogo.status === "agendado" ? "SCHEDULED" : jogo.status.toUpperCase()
          }
        })

        console.log("🖼️ Banner do bolão:", {
          bolaoId: bolaoAtivo?.id,
          bannerImage: bolaoAtivo?.banner_image,
          bannerFinal: adminBanners[0]?.image
        })

        setBanners(adminBanners)
        setMatches(jogosFormatados)
        setBolaoAtual(bolaoAtivo)

        const apostasIniciais = jogosFormatados.map((match: any) => ({
          match_id: match.id,
          resultado: null as "casa" | "empate" | "fora" | null,
        }))
        setApostas(apostasIniciais)

        // Verificar status do bolão
        await checkBolaoStatus(bolaoAtivo.id)

        console.log("✅ Dados reais carregados:", {
          bolao: bolaoAtivo.nome,
          jogos: jogosFormatados.length,
          apostas: apostasIniciais.length,
          campeonatos: [...new Set(jogosFormatados.map((j: any) => j.competitionName))],
          jogosSelecionados: "Apenas jogos selecionados no bolão"
        })
      } else {
        // Fallback para dados mockados se não houver bolões ativos
        console.log("⚠️ Nenhum bolão ativo encontrado, criando dados de teste...")

        const adminBanners: BannerSlide[] = [
          {
            id: 1,
            title: "BOLÃO BRASIL - TESTE",
            subtitle: "Dados de teste com times reais!",
            image: "/placeholder.svg?height=200&width=400",
            color: "from-green-600 to-blue-600",
            active: true,
            order: 1,
          },
        ]

        // Criar jogos de teste com times brasileiros conhecidos
        const jogosTesteBrasil: Match[] = [
          {
            id: 1001,
            competition: "brasileirao-serie-a",
            competitionName: "Campeonato Brasileiro Série A",
            homeTeam: { id: 1, name: "Flamengo", shortName: "FLA", crest: "https://crests.football-data.org/1783.png" },
            awayTeam: { id: 2, name: "Palmeiras", shortName: "PAL", crest: "https://crests.football-data.org/1769.png" },
            utcDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            status: "SCHEDULED"
          },
          {
            id: 1002,
            competition: "brasileirao-serie-a",
            competitionName: "Campeonato Brasileiro Série A",
            homeTeam: { id: 3, name: "Corinthians", shortName: "COR", crest: "https://crests.football-data.org/1779.png" },
            awayTeam: { id: 4, name: "São Paulo", shortName: "SAO", crest: "https://crests.football-data.org/1776.png" },
            utcDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
            status: "SCHEDULED"
          },
          {
            id: 1003,
            competition: "brasileirao-serie-a",
            competitionName: "Campeonato Brasileiro Série A",
            homeTeam: { id: 5, name: "Santos", shortName: "SAN", crest: "https://crests.football-data.org/6685.png" },
            awayTeam: { id: 6, name: "Botafogo", shortName: "BOT", crest: "https://crests.football-data.org/1770.png" },
            utcDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
            status: "SCHEDULED"
          },
          {
            id: 1004,
            competition: "brasileirao-serie-a",
            competitionName: "Campeonato Brasileiro Série A",
            homeTeam: { id: 7, name: "Vasco da Gama", shortName: "VAS", crest: "https://crests.football-data.org/1780.png" },
            awayTeam: { id: 8, name: "Fluminense", shortName: "FLU", crest: "https://crests.football-data.org/1765.png" },
            utcDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
            status: "SCHEDULED"
          },
          {
            id: 1005,
            competition: "brasileirao-serie-a",
            competitionName: "Campeonato Brasileiro Série A",
            homeTeam: { id: 9, name: "Grêmio", shortName: "GRE", crest: "https://crests.football-data.org/1767.png" },
            awayTeam: { id: 10, name: "Internacional", shortName: "INT", crest: "https://crests.football-data.org/6684.png" },
            utcDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
            status: "SCHEDULED"
          }
        ]

        setBanners(adminBanners)
        setMatches(jogosTesteBrasil)

        const apostasIniciais = jogosTesteBrasil.map((match) => ({
          match_id: match.id,
          resultado: null as "casa" | "empate" | "fora" | null,
        }))
        setApostas(apostasIniciais)

        console.log("✅ Dados de teste criados com times brasileiros reais")
      }
    } catch (error) {
      console.error("❌ Erro ao carregar dados:", error)

      // Fallback para dados mockados em caso de erro
      const adminBanners: BannerSlide[] = [
        {
          id: 1,
          title: "ERRO DE CONEXÃO",
          subtitle: "Verifique sua conexão com o banco de dados",
          image: "/placeholder.svg?height=200&width=400",
          color: "from-red-600 to-orange-600",
          active: true,
          order: 1,
        },
      ]

      // Carregar dados reais da API


      await new Promise((resolve) => setTimeout(resolve, 1000))

      setBanners(adminBanners.filter((b) => b.active).sort((a, b) => a.order - b.order))

      // Carregar partidas reais da Football Data API
      console.log('🏆 Carregando partidas reais da Football Data API...')
      let finalMatches: Match[] = []

      try {
        const footballResponse = await fetch('/api/football/matches?competitions=PL,PD,SA,BL1,FL1,CL&status=SCHEDULED,LIVE')
        const footballData = await footballResponse.json()

        if (footballData.success && footballData.matches.length > 0) {
          finalMatches = footballData.matches.slice(0, 20) // Limitar a 20 partidas
          console.log(`✅ ${finalMatches.length} partidas reais carregadas da Football Data API`)
        } else {
          console.log('⚠️ Nenhuma partida real encontrada, usando dados mockados')
        }
      } catch (apiError) {
        console.error('❌ Erro ao carregar partidas reais:', apiError)
        console.log('🔄 Usando dados mockados como fallback')
      }

      setMatches(finalMatches)

      const apostasIniciais = finalMatches.map((match) => ({
        match_id: match.id,
        resultado: null as "casa" | "empate" | "fora" | null,
      }))
      setApostas(apostasIniciais)

      console.log("✅ Dados da aplicação carregados:", {
        banners: adminBanners.length,
        matches: finalMatches.length,
        apostas: apostasIniciais.length,
        dataSource: finalMatches.length > 0 ? 'real' : 'vazio'
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (banners.length > 0) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % banners.length)
      }, 5000)
      return () => clearInterval(timer)
    }
  }, [banners.length])

  const handleApostaChange = (matchId: number, resultado: "casa" | "empate" | "fora") => {
    console.log("🎯 Aposta alterada:", { matchId, resultado })

    setApostas((prev) => {
      const existingIndex = prev.findIndex((a) => a.match_id === matchId)
      if (existingIndex >= 0) {
        const newApostas = [...prev]
        newApostas[existingIndex] = { match_id: matchId, resultado }
        console.log("✏️ Aposta atualizada:", newApostas[existingIndex])
        return newApostas
      } else {
        const newAposta = { match_id: matchId, resultado }
        console.log("➕ Nova aposta adicionada:", newAposta)
        return [...prev, newAposta]
      }
    })
  }

  const getApostaAtual = (matchId: number) => {
    return apostas.find((a) => a.match_id === matchId)?.resultado || null
  }

  const getApostasFeitas = () => {
    return apostas.filter((a) => a.resultado !== null).length
  }

  const podeFinalizarAposta = () => {
    return getApostasFeitas() >= 11 && !apostasEncerradas
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % banners.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + banners.length) % banners.length)
  }

  const getMatchesByCompetition = (competitionId: string) => {
    return matches.filter((match) => match.competition === competitionId)
  }

  const getCompetitionsWithMatches = () => {
    const competitionsMap = new Map()

    matches.forEach((match) => {
      if (!competitionsMap.has(match.competition)) {
        competitionsMap.set(match.competition, {
          id: match.competition,
          name: match.competitionName || match.competition.replace(/-/g, ' ').toUpperCase(),
          matches: []
        })
      }
      competitionsMap.get(match.competition).matches.push(match)
    })

    return Array.from(competitionsMap.values()).filter(comp => comp.matches.length > 0)
  }

  const formatDateTime = (utcDate: string) => {
    const date = new Date(utcDate)
    const day = date.getDate().toString().padStart(2, '0')
    const month = date.toLocaleDateString("pt-BR", { month: "short" }).replace('.', '')
    const time = date.toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" })

    return {
      date: `${day} de ${month}`,
      time: time,
    }
  }

  const handleCPFChange = (value: string) => {
    const formatted = formatCPF(value)
    setRegisterForm({ ...registerForm, cpf: formatted })

    // Validar CPF em tempo real
    const errorMessage = getCPFErrorMessage(formatted)
    setCpfError(errorMessage || "")
  }

  const handleLogin = async () => {
    if (!loginForm.email || !loginForm.senha) {
      setError("Preencha todos os campos")
      return
    }

    setLoginLoading(true)
    setError("")

    try {
      // Verificar se é admin
      if (loginForm.email === "<EMAIL>" && loginForm.senha === "admin123") {
        const adminUser = {
          id: 1,
          nome: "Administrador",
          email: "<EMAIL>",
          tipo: "admin",
        }
        localStorage.setItem("user", JSON.stringify(adminUser))
        window.location.href = "/admin"
        return
      }

      console.log("📤 Enviando dados de login:", { email: loginForm.email })

      // Chamar API real de login
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginForm.email,
          senha: loginForm.senha
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro no login')
      }

      if (!result.success) {
        throw new Error(result.message || 'Erro no login')
      }

      console.log("✅ Login realizado com sucesso:", result)

      // Verificar se é cambista e mostrar log especial
      if (result.user.tipo === "cambista") {
        console.log("🏪 CAMBISTA LOGADO - Impressão automática ativada:", result.user.nome)
        toast.success(`🏪 Bem-vindo, Cambista ${result.user.nome}! Impressão automática ativada.`)
      } else {
        toast.success("Login realizado com sucesso!")
      }

      // Armazenar dados do usuário
      localStorage.setItem("user", JSON.stringify(result.user))
      setUser(result.user)
      await loadUserData(result.user.id)

      setShowLoginDialog(false)
      setLoginForm({ email: "", senha: "" })
    } catch (err) {
      setError("Erro ao fazer login. Tente novamente.")
      console.error("❌ Erro no login:", err)
    } finally {
      setLoginLoading(false)
    }
  }

  const handleRegister = async () => {
    if (!registerForm.nome || !registerForm.email || !registerForm.telefone || !registerForm.senha) {
      setError("Preencha todos os campos")
      return
    }

    // Validar CPF
    const cpfErrorMessage = getCPFErrorMessage(registerForm.cpf)
    if (cpfErrorMessage) {
      setError(cpfErrorMessage)
      setCpfError(cpfErrorMessage)
      return
    }

    if (registerForm.senha.length < 6) {
      setError("Senha deve ter pelo menos 6 caracteres")
      return
    }

    if (registerForm.senha !== registerForm.confirmarSenha) {
      setError("Senhas não coincidem")
      return
    }

    setRegisterLoading(true)
    setError("")

    try {
      // Preparar dados para API
      const affiliateRef = localStorage.getItem('affiliate_ref') || codigoAfiliado

      const registrationData = {
        nome: registerForm.nome,
        email: registerForm.email,
        telefone: registerForm.telefone,
        cpf: registerForm.cpf,
        senha: registerForm.senha,
        affiliate_code: affiliateRef
      }

      console.log("📤 Enviando dados de registro:", registrationData)

      // Chamar API real de registro
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao registrar usuário')
      }

      if (!result.success) {
        throw new Error(result.message || 'Erro ao registrar usuário')
      }

      console.log("✅ Usuário registrado com sucesso:", result)

      // Armazenar dados do usuário
      localStorage.setItem("user", JSON.stringify(result.user))
      setUser(result.user)
      await loadUserData(result.user.id)

      // Limpar código de afiliado
      if (affiliateRef) {
        localStorage.removeItem('affiliate_ref')
        setCodigoAfiliado(null)
      }

      // Mensagem de sucesso
      if (result.is_affiliate_referral) {
        toast.success("Conta criada com sucesso! Você foi indicado por um afiliado.")
        // Redirecionar para dashboard após 2 segundos
        setTimeout(() => {
          window.location.href = "/dashboard"
        }, 2000)
      } else {
        toast.success("Conta criada com sucesso!")
      }

      setShowRegisterDialog(false)
      setRegisterForm({
        nome: "",
        email: "",
        telefone: "",
        cpf: "",
        senha: "",
        confirmarSenha: "",
      })
      setCpfError("")
    } catch (err) {
      setError("Erro ao criar conta. Tente novamente.")
      console.error("❌ Erro no registro:", err)
    } finally {
      setRegisterLoading(false)
    }
  }

  const handleLogout = () => {
    try {
      localStorage.removeItem("user")
      setUser(null)
      setUserBilhetes([])
      setUserPagamentos([])
      // Resetar apostas
      setApostas(apostas.map((a) => ({ ...a, resultado: null })))
      toast.success("Logout realizado com sucesso!")
      console.log("✅ Logout realizado")
    } catch (error) {
      console.error("❌ Erro no logout:", error)
    }
  }

  const handleFinalizarAposta = () => {
    if (!podeFinalizarAposta()) {
      setError("Selecione pelo menos 11 partidas para finalizar a aposta")
      toast.error("Selecione pelo menos 11 partidas")
      return
    }

    if (!user) {
      setShowLoginDialog(true)
      return
    }

    setShowPaymentDialog(true)
    generatePixQRCode()
  }

  const generatePixQRCode = async () => {
    setPaymentLoading(true)
    setError("")

    try {
      // Verificar se o usuário está logado
      if (!user || !user.id) {
        setError("Você precisa estar logado para fazer apostas")
        toast.error("Faça login para continuar")
        setShowLoginDialog(true)
        return
      }



      // Validar dados obrigatórios do usuário
      if (!user.nome || !user.email) {
        setError("Dados do usuário incompletos. Faça login novamente.")
        toast.error("Dados do usuário incompletos")
        setShowLoginDialog(true)
        return
      }

      // Garantir que o CPF existe e não está vazio
      const userCpf = user.cpf && user.cpf.trim() !== "" ? user.cpf : "00000000000"

      console.log("🔄 Gerando QR Code PIX...")
      console.log("👤 Dados do usuário para PIX:", {
        nome: user.nome,
        email: user.email,
        cpf: userCpf,
        cpf_original: user.cpf
      })

      // Dados do usuário para o PIX
      const pixRequest = {
        value: valorAposta, // Valor fixo da aposta do bolão
        description: `Bolão Brasil - ${getApostasFeitas()} apostas`,
        client_name: user.nome,
        client_email: user.email,
        client_document: userCpf,
        qrcode_image: true
      }

      console.log("📤 Enviando dados PIX:", pixRequest)

      // Fazer requisição para a API PIX real
      const response = await fetch('/api/pix/qrcode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pixRequest)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erro ao gerar QR Code PIX')
      }

      const pixResponse = await response.json()

      if (!pixResponse.success) {
        throw new Error(pixResponse.error || 'Erro na resposta da API PIX')
      }

      console.log("✅ QR Code PIX gerado com sucesso:", pixResponse.data)

      setPixData({
        qr_code_value: pixResponse.data?.qr_code_value || "",
        qrcode_image: pixResponse.data?.qrcode_image || "",
        expiration_datetime: pixResponse.data?.expiration_datetime || new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        status: pixResponse.data?.status || "pending",
        transaction_id: pixResponse.data?.transaction_id || `BLT${Date.now()}${Math.random().toString(36).substring(2, 11).toUpperCase()}`,
        order_id: pixResponse.data?.order_id || pixResponse.data?.transaction_id || ""
      })

      // Salvar bilhete no banco de dados imediatamente
      const apostasFeitas = apostas.filter((a) => a.resultado !== null)

      // Enriquecer apostas com dados dos jogos
      const apostasEnriquecidas = apostasFeitas.map(aposta => {
        const jogo = matches.find(m => m.id === aposta.match_id)
        return {
          jogo_id: aposta.match_id,
          palpite: aposta.resultado,
          jogo_nome: jogo ? `${jogo.homeTeam.name} vs ${jogo.awayTeam.name}` : `Jogo ${aposta.match_id}`,
          odd: 1.5
        }
      })

      // Garantir que os dados do cliente não estejam vazios
      const clientName = user?.nome?.trim() || "Usuário Padrão"
      const clientEmail = user?.email?.trim() || "<EMAIL>"
      const clientDocument = user?.cpf?.trim() || "00000000000"

      const bilheteData = {
        user_id: user?.id,
        apostas: apostasEnriquecidas,
        valor: valorAposta, // Valor fixo da aposta do bolão
        qr_code_pix: pixResponse.data?.qr_code_value || "",
        transaction_id: pixResponse.data?.transaction_id || "",
        pix_order_id: pixResponse.data?.order_id || "",
        client_name: clientName,
        client_email: clientEmail,
        client_document: clientDocument,
        cambista_id: user?.tipo === "cambista" ? user.id : null
      }

      console.log("💾 Salvando bilhete no banco:", bilheteData)

      const saveBilheteResponse = await fetch('/api/bilhetes/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bilheteData)
      })

      if (!saveBilheteResponse.ok) {
        const errorData = await saveBilheteResponse.json()
        console.error("❌ Erro ao salvar bilhete:", errorData)

        // Tratamento específico para erro de conexão
        if (errorData.details && errorData.details.includes("Too many connections")) {
          // Aguardar 2 segundos e tentar novamente automaticamente
          await new Promise(resolve => setTimeout(resolve, 2000))
          throw new Error("Sistema temporariamente sobrecarregado. Tentando novamente...")
        }

        throw new Error(errorData.message || errorData.error || 'Erro ao salvar bilhete')
      }

      const bilheteResult = await saveBilheteResponse.json()
      console.log("✅ Bilhete salvo no banco:", bilheteResult)

      // Criar objeto do bilhete para o frontend
      const novoBilhete: BilheteAposta = {
        id: bilheteResult.bilhete.codigo,
        data: new Date().toLocaleDateString("pt-BR"),
        hora: new Date().toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" }),
        apostas: apostas
          .filter((a) => a.resultado !== null)
          .map((a) => {
            const match = matches.find((m) => m.id === a.match_id)
            return {
              jogo: `${match?.homeTeam.name || 'Time Casa'} x ${match?.awayTeam.name || 'Time Fora'}`,
              resultado: a.resultado === "casa" ? "Casa" : a.resultado === "empate" ? "Empate" : "Fora",
            }
          }),
        valor: bilheteResult.bilhete.valor,
        status: bilheteResult.bilhete.status,
        qr_code_pix: bilheteResult.bilhete.qr_code_pix,
        transaction_id: bilheteResult.bilhete.transaction_id,
      }

      setBilhete(novoBilhete)
      setUserBilhetes((prev) => [novoBilhete, ...prev])

      toast.success("Bilhete criado! Aguardando pagamento PIX.")
      console.log("✅ Bilhete criado:", novoBilhete.id)

      // Iniciar verificação de status do pagamento
      if (bilheteResult.bilhete.transaction_id) {
        startPaymentStatusCheck(bilheteResult.bilhete.transaction_id)
      }
    } catch (error) {
      console.error("❌ Erro ao gerar PIX:", error)

      // Tratamento específico de erros
      let errorMessage = "Erro ao gerar pagamento PIX. Tente novamente."
      if (error instanceof Error) {
        if (error.message.includes("sobrecarregado")) {
          errorMessage = "Sistema temporariamente sobrecarregado. Aguarde alguns segundos e tente novamente."
        } else if (error.message.includes("conexão")) {
          errorMessage = "Problema de conexão. Verifique sua internet e tente novamente."
        }
      }

      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setPaymentLoading(false)
    }
  }

  const startPaymentStatusCheck = (transactionId: string) => {
    if (!transactionId) return

    console.log("🔄 Iniciando verificação de status do pagamento:", transactionId)

    const checkInterval = setInterval(async () => {
      try {
        // 1. Primeiro, verificar status via API PIX
        console.log("🔍 Verificando status PIX via API...")
        const statusResponse = await fetch(`/api/pix/check-status?transaction_id=${transactionId}`)

        if (statusResponse.ok) {
          const statusData = await statusResponse.json()
          console.log("💳 Resposta da API de status:", statusData)

          // Se o status foi atualizado para pago, recarregar bilhetes
          if (statusData.updated && statusData.status === 'pago') {
            console.log("✅ Status atualizado para pago via API!")

            // Disparar webhook interno para garantir processamento
            try {
              await fetch('/api/v1/MP/webhookruntransation', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  order_id: transactionId,
                  status: 'PAID',
                  type: 'PIXOUT',
                  message: 'Payment confirmed via polling'
                })
              })
              console.log('📡 Webhook interno disparado para garantir processamento')
            } catch (webhookError) {
              console.log('⚠️ Erro no webhook interno:', webhookError)
            }
          }
        }

        // 2. Consultar bilhetes atualizados do banco
        const response = await fetch(`/api/user/bilhetes?user_id=${user?.id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (response.ok) {
          const bilhetesData = await response.json()
          console.log("📊 Bilhetes do usuário:", bilhetesData)

          // Procurar bilhete com este transaction_id
          const bilheteAtual = bilhetesData.bilhetes?.find((b: any) => {
            return b.transaction_id === transactionId || b.codigo === transactionId
          })

          console.log("🔍 Bilhete encontrado:", bilheteAtual)

          // Verificar se pagamento foi aprovado
          if (bilheteAtual && (bilheteAtual.status === 'pago' || bilheteAtual.status === 'PAID' || bilheteAtual.status === 'aprovado')) {
            clearInterval(checkInterval)

            // Atualizar status do bilhete para "pago" (que será exibido como "Aprovado")
            setUserBilhetes(prev =>
              prev.map((b: any) =>
                (b.transaction_id === transactionId || b.codigo === transactionId)
                  ? { ...b, status: "pago" }
                  : b
              )
            )

            // Adicionar pagamento à lista de pagamentos do usuário
            const novoPagamento: Pagamento = {
              id: Date.now().toString(),
              data: new Date().toLocaleDateString('pt-BR'),
              valor: parseFloat(bilheteAtual.valor_total || bilheteAtual.valor || "0"),
              status: "aprovado",
              metodo: "PIX",
              bilhete_id: bilheteAtual.codigo
            }

            setUserPagamentos(prev => [novoPagamento, ...prev])

            // Atualizar estatísticas do usuário
            setUserStats(prev => ({
              ...prev,
              totalApostas: prev.totalApostas + 1,
              totalGasto: prev.totalGasto + novoPagamento.valor,
              boloesParticipados: prev.boloesParticipados + 1
            }))

            // Atualizar pixData para mostrar como confirmado
            setPixData(prev => prev ? { ...prev, status: "confirmed" } : null)

            // Notificação de sucesso mais visível
            toast.success("🎉 PAGO COM SUCESSO! Bilhete aprovado.", {
              duration: 8000,
              style: {
                background: '#10B981',
                color: 'white',
                fontSize: '16px',
                fontWeight: 'bold',
                padding: '16px',
                borderRadius: '12px',
                boxShadow: '0 10px 25px rgba(16, 185, 129, 0.3)'
              }
            })

            // Tocar som de sucesso
            playSuccessSound()

            // Disparar webhook interno para garantir processamento
            try {
              await fetch('/api/v1/MP/webhookruntransation', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  order_id: transactionId,
                  status: 'PAID',
                  type: 'PIXOUT',
                  message: 'Payment confirmed - showing success modal'
                })
              })
              console.log('📡 Webhook final disparado - pagamento confirmado')
            } catch (webhookError) {
              console.log('⚠️ Erro no webhook final:', webhookError)
            }

            // Mostrar modal de sucesso
            setPaymentSuccessData({
              codigo: bilheteAtual.codigo,
              valor: bilheteAtual.valor_total || bilheteAtual.valor || "0,00",
              data: new Date().toLocaleString('pt-BR'),
              transactionId: transactionId
            })
            setShowPaymentSuccess(true)

            // Mostrar SweetAlert2 de sucesso
            showPaymentSuccessAlert({
              codigo: bilheteAtual.codigo,
              valor: bilheteAtual.valor_total || bilheteAtual.valor || "0,00",
              clientName: user?.nome,
              transactionId: transactionId
            })

            // IMPRESSÃO AUTOMÁTICA PARA CAMBISTAS
            if (user?.tipo === "cambista" && bilheteAtual) {
              console.log("🖨️ CAMBISTA DETECTADO - Iniciando impressão automática...")
              try {
                // Aguardar um momento para garantir que o bilhete foi atualizado
                setTimeout(async () => {
                  await printBilheteAutomatico(bilheteAtual)
                }, 2000)
              } catch (printError) {
                console.error("❌ Erro na impressão automática:", printError)
                toast.error("Erro na impressão automática. Use o botão manual.")
              }
            }

            // Fechar modal de pagamento PIX se estiver aberto
            setShowPaymentDialog(false)

            console.log("✅ Pagamento confirmado para:", transactionId)
          }
        }
      } catch (error) {
        console.error("❌ Erro ao verificar status:", error)
      }
    }, 5000) // Verificar a cada 5 segundos

    // Parar verificação após 10 minutos
    setTimeout(() => {
      clearInterval(checkInterval)
      console.log("⏰ Timeout na verificação de pagamento:", transactionId)
    }, 600000)
  }

  const copyPixCode = async () => {
    if (pixData?.qr_code_value) {
      try {
        await navigator.clipboard.writeText(pixData.qr_code_value)
        setPixCodeCopied(true)
        toast.success("Código PIX copiado para pagamento!")
        setTimeout(() => setPixCodeCopied(false), 3000)
        console.log("✅ Código PIX copiado")
      } catch (error) {
        console.error("❌ Erro ao copiar:", error)
        // Fallback para navegadores mais antigos
        try {
          const textArea = document.createElement("textarea")
          textArea.value = pixData.qr_code_value
          textArea.style.position = "fixed"
          textArea.style.left = "-999999px"
          textArea.style.top = "-999999px"
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          const successful = document.execCommand("copy")
          document.body.removeChild(textArea)

          if (successful) {
            setPixCodeCopied(true)
            toast.success("Código PIX copiado para pagamento!")
            setTimeout(() => setPixCodeCopied(false), 3000)
          } else {
            toast.error("Erro ao copiar código PIX")
          }
        } catch (fallbackError) {
          console.error("❌ Erro no fallback:", fallbackError)
          toast.error("Não foi possível copiar automaticamente. Copie manualmente.")
        }
      }
    }
  }

  // Função de impressão automática para cambistas
  const printBilheteAutomatico = async (bilheteData: any) => {
    try {
      console.log("🖨️ IMPRESSÃO AUTOMÁTICA CAMBISTA - Bilhete:", bilheteData.codigo || bilheteData.id)

      // Buscar apostas detalhadas do bilhete com nomes reais dos times
      let apostasParaImprimir = []

      try {
        // Tentar buscar apostas detalhadas da API
        const response = await fetch(`/api/bilhetes/${bilheteData.codigo || bilheteData.id}/apostas`)
        if (response.ok) {
          const data = await response.json()
          apostasParaImprimir = data.apostas || []
          console.log("✅ Apostas detalhadas carregadas:", apostasParaImprimir.length)
        }
      } catch (error) {
        console.log("⚠️ Erro ao buscar apostas detalhadas, usando fallback:", error)
      }

      // Fallback: usar apostas do estado atual se não conseguir buscar da API
      if (!apostasParaImprimir || apostasParaImprimir.length === 0) {
        apostasParaImprimir = apostas
          .filter((a) => a.resultado !== null)
          .map((a) => {
            const match = matches.find((m) => m.id === a.match_id)
            return {
              jogo: `${match?.homeTeam.name || 'Time Casa'} x ${match?.awayTeam.name || 'Time Fora'}`,
              resultado: a.resultado === "casa" ? "Casa" : a.resultado === "empate" ? "Empate" : "Fora"
            }
          })
        console.log("⚠️ Usando apostas do estado atual:", apostasParaImprimir.length)
      }

      // Criar conteúdo do bilhete para impressão térmica 57mm (FORMATO SOLICITADO)
      const bilheteContent = `
==============================
      BOLÃO BRASIL
==============================
Bilhete: ${bilheteData.codigo || bilheteData.id}
Data: ${new Date().toLocaleDateString('pt-BR')} ${new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
------------------------------
SUAS APOSTAS:
==============================
${apostasParaImprimir
  .map((a: any, i: number) => {
    // Quebrar nomes longos para impressora 57mm (30 chars)
    const jogoFormatado = a.jogo.length > 30 ?
      a.jogo.substring(0, 30) : a.jogo

    return `${i + 1}. ${jogoFormatado}
   Aposta: ${a.resultado}
==============================`
  })
  .join("\n")}
------------------------------
Valor: R$ ${(bilheteData.valor_total || bilheteData.valor || 0).toFixed(2)}
------------------------------
Boa sorte!
==============================`

      console.log("📄 Conteúdo do bilhete (CAMBISTA):", bilheteContent)

      // Criar download automático do bilhete
      const blob = new Blob([bilheteContent], { type: "text/plain" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `bilhete_cambista_${bilheteData.codigo || bilheteData.id}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast.success("🖨️ Bilhete impresso automaticamente para cambista!")
      console.log("✅ Impressão automática concluída para cambista")

    } catch (error) {
      console.error("❌ Erro na impressão automática:", error)
      toast.error("Erro na impressão automática")
    }
  }

  const printBilhete = async () => {
    if (!bilhete) return

    setPrintingBilhete(true)

    try {
      console.log("🖨️ Imprimindo bilhete:", bilhete.id)
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // Criar conteúdo do bilhete para impressão térmica 57mm (FORMATO SOLICITADO)
      const bilheteContent = `
==============================
      BOLÃO BRASIL
==============================
Bilhete: ${bilhete.id}
Data: ${bilhete.data} ${bilhete.hora}
------------------------------
SUAS APOSTAS:
==============================
${bilhete.apostas
  .map((a, i) => {
    // Quebrar nomes longos para impressora 57mm (30 chars)
    const jogoFormatado = a.jogo.length > 30 ?
      a.jogo.substring(0, 30) : a.jogo
    return `${i + 1}. ${jogoFormatado}
   Aposta: ${a.resultado}
==============================`
  })
  .join("\n")}
------------------------------
Valor: R$ ${bilhete.valor.toFixed(2)}
------------------------------
Boa sorte!
==============================
      `

      console.log("📄 Conteúdo do bilhete:", bilheteContent)

      // Criar download do bilhete
      const blob = new Blob([bilheteContent], { type: "text/plain" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `bilhete_${bilhete.id}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast.success("Bilhete impresso e baixado com sucesso!")

      // Resetar apostas após impressão
      setApostas(apostas.map((a) => ({ ...a, resultado: null })))
      setShowBilheteDialog(false)
      console.log("✅ Bilhete impresso e apostas resetadas")
    } catch (error) {
      console.error("❌ Erro ao imprimir bilhete:", error)
      setError("Erro ao imprimir bilhete")
      toast.error("Erro ao imprimir bilhete")
    } finally {
      setPrintingBilhete(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pendente":
        return <Badge variant="secondary">Pendente</Badge>
      case "pago":
      case "aprovado":
        return <Badge className="bg-green-600">Aprovado</Badge>
      case "ganhou":
        return <Badge className="bg-green-600">Ganhou</Badge>
      case "perdeu":
        return <Badge variant="destructive">Perdeu</Badge>
      case "rejeitado":
      case "cancelado":
        return <Badge variant="destructive">Rejeitado</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleBilheteClick = (bilheteClicado: BilheteAposta) => {
    console.log("🔍 Abrindo detalhes do bilhete:", bilheteClicado.id)
    setSelectedBilhete(bilheteClicado)
    setShowBilheteDialog(true)
  }

  const handleOpenMeusBilhetes = () => {
    console.log("📋 Abrindo Meus Bilhetes")
    setShowUserBilhetes(true)
  }

  const handleOpenMeusPagamentos = () => {
    console.log("💳 Abrindo Meus Pagamentos")
    setShowUserPagamentos(true)
  }

  const handleOpenMeuPerfil = () => {
    console.log("👤 Abrindo Meu Perfil")
    setShowUserProfile(true)
  }

  const handleOpenMeuAfiliado = () => {
    console.log("🔗 Abrindo Meu Afiliado")
    window.open("/dashboard/afiliado", "_blank")
  }



  // Verificar se CPF é válido para mostrar ícone
  const isCPFValid = registerForm.cpf && validateCPF(registerForm.cpf)
  const showCPFValidation = registerForm.cpf.length >= 11

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-green-500 mx-auto mb-4" />
          <p className="text-white">Carregando jogos...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <style jsx>{`
        .banner-container {
          aspect-ratio: 16/9;
          min-height: 200px;
          max-height: 400px;
        }

        @media (max-width: 640px) {
          .banner-container {
            aspect-ratio: 16/10;
            min-height: 180px;
            max-height: 280px;
          }
        }

        @media (min-width: 641px) and (max-width: 1024px) {
          .banner-container {
            aspect-ratio: 20/9;
            min-height: 220px;
            max-height: 320px;
          }
        }

        @media (min-width: 1025px) {
          .banner-container {
            aspect-ratio: 24/9;
            min-height: 250px;
            max-height: 400px;
          }
        }

        .banner-image {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          object-fit: cover;
          object-position: center;
          width: 100%;
          height: 100%;
        }
      `}</style>

      <div className="min-h-screen bg-slate-900 text-white overflow-x-hidden">
        {/* Banner de Afiliado */}
        {codigoAfiliado && !user && (
          <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-3 text-center">
            <div className="flex items-center justify-center gap-2">
              <UserPlus className="h-5 w-5" />
              <span className="font-medium">
                🎉 Você foi convidado por um afiliado! Registre-se e ganhe benefícios especiais!
              </span>
            </div>
          </div>
        )}

        {/* Header */}
        <header className="bg-slate-800 border-b border-slate-700 sticky top-0 z-50 shadow-lg">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4">
            <div className="flex items-center justify-between">
              {/* Logo/Title - Centralizado em mobile */}
              <div className="flex-1 flex justify-center sm:justify-start">
                <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-green-400 text-center sm:text-left">
                  Sistema Bolão
                </h1>
              </div>

              {/* User Menu - Posicionado à direita */}
              <div className="flex items-center gap-2 sm:gap-4 absolute right-3 sm:relative sm:right-0">
                {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className={`${user.tipo === 'cambista' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-green-600 hover:bg-green-700'} text-white px-3 sm:px-4 md:px-6 py-2 md:py-3 rounded-lg font-medium transition-colors flex items-center gap-1 sm:gap-2 text-sm sm:text-base md:text-lg mobile-friendly touch-button`}>
                    <User className="h-4 w-4 md:h-5 md:w-5" />
                    <span className="hidden sm:inline truncate max-w-[120px] md:max-w-[200px]">
                      {user.tipo === 'cambista' ? '🏪 ' : ''}{user.nome}
                    </span>
                    <span className="sm:hidden truncate max-w-[80px]">
                      {user.tipo === 'cambista' ? '🏪 ' : ''}{user.nome.split(' ')[0]}
                    </span>
                    <ChevronDown className="h-4 w-4 md:h-5 md:w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-slate-800 border-slate-700">
                  <DropdownMenuLabel className="text-white">Minha Conta</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  <DropdownMenuItem
                    onClick={handleOpenMeusBilhetes}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <Receipt className="mr-2 h-4 w-4" />
                    Meus Bilhetes
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleOpenMeusPagamentos}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    Meus Pagamentos
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleOpenMeuAfiliado}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    Meu Afiliado
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleOpenMeuPerfil}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <User className="mr-2 h-4 w-4" />
                    Meu Perfil
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="text-red-400 hover:bg-slate-700 hover:text-red-300 cursor-pointer"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sair
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                onClick={() => setShowLoginDialog(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-3 sm:px-4 md:px-6 py-2 md:py-3 rounded-lg font-medium transition-colors text-sm sm:text-base md:text-lg mobile-friendly touch-button"
              >
                <span className="hidden sm:inline">Entrar</span>
                <span className="sm:hidden">Login</span>
              </Button>
            )}
            </div>
          </div>
        </div>
      </header>

      {/* Banner Carousel ou Ranking */}
      {bolaoFinalizado && ranking.length > 0 ? (
        /* Ranking de Apostadores */
        <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl sm:text-3xl font-bold text-white text-center mb-6">
              🏆 Ranking Final - {bolaoAtual?.nome}
            </h2>
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Posição
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Apostador
                      </th>
                      <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Acertos
                      </th>
                      <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        %
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {ranking.map((apostador, index) => (
                      <tr
                        key={apostador.id}
                        className={`hover:bg-gray-50 cursor-pointer ${
                          index === 0 ? 'bg-yellow-50' :
                          index === 1 ? 'bg-gray-50' :
                          index === 2 ? 'bg-orange-50' : ''
                        }`}
                        onClick={() => {
                          window.open(`/bolao/${bolaoAtual?.id}/apostas/${apostador.id}`, '_blank')
                        }}
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {index === 0 && <span className="text-2xl mr-2">🥇</span>}
                            {index === 1 && <span className="text-2xl mr-2">🥈</span>}
                            {index === 2 && <span className="text-2xl mr-2">🥉</span>}
                            <span className="text-sm font-medium text-gray-900">
                              {index + 1}º
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {apostador.nome}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-center">
                          <span className="text-sm font-bold text-green-600">
                            {apostador.acertos}/{apostador.total_apostas}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-center">
                          <span className="text-sm text-gray-900">
                            {apostador.percentual_acertos}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            <p className="text-center text-white text-sm mt-4 opacity-90">
              Clique em um apostador para ver suas apostas
            </p>
          </div>
        </div>
      ) : banners.length > 0 ? (
        /* Banner Carousel Sempre Responsivo */
        <div className="banner-container relative w-full aspect-[16/9] sm:aspect-[21/9] md:aspect-[24/9] lg:aspect-[28/9] overflow-hidden shadow-lg">
          <div
            className="flex transition-transform duration-700 ease-in-out h-full"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {banners.map((banner) => (
              <div
                key={banner.id}
                className={`min-w-full h-full bg-gradient-to-r ${banner.color} flex items-center justify-center relative overflow-hidden`}
              >
                {/* Imagem de fundo otimizada */}
                {banner.image && banner.image !== "/placeholder.svg?height=200&width=400" && (
                  <div className="absolute inset-0">
                    <img
                      src={banner.image}
                      alt={banner.title}
                      className="banner-image w-full h-full object-cover object-center"
                      style={{
                        filter: 'contrast(1.1) brightness(0.9) saturate(1.1)',
                        transform: 'translateZ(0)'
                      }}
                      loading="eager"
                      decoding="async"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-black/50"></div>
                  </div>
                )}

                {/* Banner sem texto sobreposto */}
              </div>
            ))}
          </div>

          {/* Navigation Arrows melhorados */}
          {banners.length > 1 && (
            <>
              <button
                onClick={prevSlide}
                className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-black/40 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 sm:p-3 transition-all duration-300 hover:scale-110 border border-white/20"
                aria-label="Banner anterior"
              >
                <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </button>
              <button
                onClick={nextSlide}
                className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-black/40 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 sm:p-3 transition-all duration-300 hover:scale-110 border border-white/20"
                aria-label="Próximo banner"
              >
                <ChevronRight className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </button>
            </>
          )}

          {/* Dots Indicator melhorados */}
          {banners.length > 1 && (
            <div className="absolute bottom-3 sm:bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 sm:space-x-3">
              {banners.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 border border-white/30 ${
                    index === currentSlide
                      ? "bg-white scale-125 shadow-lg"
                      : "bg-white/50 hover:bg-white/70 hover:scale-110"
                  }`}
                  aria-label={`Ir para banner ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Indicador de progresso */}
          {banners.length > 1 && (
            <div className="absolute bottom-0 left-0 w-full h-1 bg-black/20">
              <div
                className="h-full bg-gradient-to-r from-yellow-400 to-orange-500 transition-all duration-700 ease-in-out"
                style={{ width: `${((currentSlide + 1) / banners.length) * 100}%` }}
              ></div>
            </div>
          )}
        </div>
      ) : null}

      {/* Main Content */}
      <main className="p-2 sm:p-4 md:p-6 lg:p-8 w-full max-w-none mx-auto">
        {getCompetitionsWithMatches().map((competition) => {
          return (
            <div key={competition.id} className="mb-8 w-full">
              {/* Competition Header */}
              <div className="bg-green-600 text-white p-3 sm:p-4 md:p-5 lg:p-6 rounded-t-lg">
                <div className="flex items-center justify-center gap-3 md:gap-4">
                  <CompetitionEmblem
                    competitionCode={(() => {
                      const id = competition.id.toLowerCase()
                      const name = competition.name.toLowerCase()

                      // Serie A italiana (verificar primeiro)
                      if (name.includes('serie a') && !name.includes('brasil')) return 'SA'

                      // Brasileirão
                      if (id.includes('brasileirao') || name.includes('brasileir')) return 'BSA'

                      // Outras competições
                      if (id.includes('premier')) return 'PL'
                      if (id.includes('la-liga') || id.includes('primera')) return 'PD'
                      if (id.includes('bundesliga')) return 'BL1'
                      if (id.includes('ligue')) return 'FL1'
                      if (id.includes('champions')) return 'CL'
                      if (id.includes('europa')) return 'EL'

                      return competition.id.toUpperCase()
                    })()}
                    alt={competition.name}
                    size="lg"
                  />
                  <h2 className="text-lg sm:text-xl font-bold">{competition.name}</h2>
                </div>
              </div>

              {/* Competition Subheader - Desktop & Tablet */}
              <div
                className="hidden sm:flex text-white p-3 md:p-4 justify-end items-center text-sm md:text-base font-medium"
                style={{ backgroundColor: "rgb(30, 64, 175)" }}
              >
                <div className="flex gap-6 md:gap-8 lg:gap-12">
                  <span className="min-w-[60px] text-center">Casa</span>
                  <span className="min-w-[60px] text-center">Empate</span>
                  <span className="min-w-[60px] text-center">Fora</span>
                </div>
              </div>

              {/* Competition Subheader - Mobile */}
              <div
                className="block sm:hidden text-white p-2 text-center text-xs font-medium"
                style={{ backgroundColor: "rgb(30, 64, 175)" }}
              >
                <span>Selecione suas apostas</span>
              </div>

              {/* Matches */}
              <div className="bg-slate-800 rounded-b-lg overflow-hidden">
                {competition.matches.map((match: any, index: number) => {
                  const { date, time } = formatDateTime(match.utcDate)
                  const apostaAtual = getApostaAtual(match.id)

                  return (
                    <div
                      key={match.id}
                      className={`p-4 sm:p-5 md:p-6 border-b border-slate-700 ${
                        index === competition.matches.length - 1 ? "border-b-0" : ""
                      }`}
                    >
                      {/* Layout Desktop & Tablet */}
                      <div className="hidden sm:flex items-center justify-between">
                        <div className="flex items-center gap-3 md:gap-4 flex-1">
                          <div className="flex items-center gap-2 md:gap-3">
                            <TeamLogo
                              src={match.homeTeam.crest}
                              alt={match.homeTeam.name}
                              size="sm"
                            />
                            <span className="font-medium text-white text-sm md:text-base lg:text-lg">{match.homeTeam.name}</span>
                          </div>
                          <span className="text-slate-400 font-bold mx-2 md:mx-3 text-sm md:text-base">VS</span>
                          <div className="flex items-center gap-2 md:gap-3">
                            <TeamLogo
                              src={match.awayTeam.crest}
                              alt={match.awayTeam.name}
                              size="sm"
                            />
                            <span className="font-medium text-white text-sm md:text-base lg:text-lg">{match.awayTeam.name}</span>
                          </div>
                        </div>

                        <div className="text-sm md:text-base text-slate-400 mx-4 md:mx-6 font-medium text-center">
                          {date} - {time}
                        </div>

                        <div className="hidden sm:flex gap-2 md:gap-3">
                          <Button
                            size="sm"
                            variant={apostaAtual === "casa" ? "default" : "outline"}
                            className={`w-20 md:w-24 lg:w-28 h-12 md:h-14 font-bold text-sm md:text-base transition-all duration-200 mobile-friendly ${
                              apostaAtual === "casa"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "casa")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-10 h-10 flex items-center justify-center">
                                {match.homeTeam.crest ? (
                                  <img
                                    src={match.homeTeam.crest}
                                    alt={match.homeTeam.name}
                                    className="w-10 h-10 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-base font-bold text-white shadow-sm border border-blue-300"
                                  style={{ display: match.homeTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.homeTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                            </div>
                          </Button>
                          <Button
                            size="sm"
                            variant={apostaAtual === "empate" ? "default" : "outline"}
                            className={`w-16 h-12 font-bold text-sm transition-all duration-200 ${
                              apostaAtual === "empate"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "empate")}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-lg font-black">=</span>
                              <span className="text-xs">Empate</span>
                            </div>
                          </Button>
                          <Button
                            size="sm"
                            variant={apostaAtual === "fora" ? "default" : "outline"}
                            className={`w-20 md:w-24 lg:w-28 h-12 md:h-14 font-bold text-sm transition-all duration-200 mobile-friendly ${
                              apostaAtual === "fora"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "fora")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-10 h-10 flex items-center justify-center">
                                {match.awayTeam.crest ? (
                                  <img
                                    src={match.awayTeam.crest}
                                    alt={match.awayTeam.name}
                                    className="w-10 h-10 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-10 h-10 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center text-base font-bold text-white shadow-sm border border-red-300"
                                  style={{ display: match.awayTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.awayTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                            </div>
                          </Button>
                        </div>
                      </div>

                      {/* Layout Mobile */}
                      <div className="block sm:hidden">
                        {/* Times e VS - Layout Horizontal Compacto para Mobile */}
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {match.homeTeam.crest ? (
                                <img
                                  src={match.homeTeam.crest}
                                  alt={match.homeTeam.name}
                                  className="w-6 h-6 object-contain"
                                  onError={(e) => {
                                    const target = e.currentTarget as HTMLImageElement
                                    target.style.display = 'none'
                                    const fallback = target.nextElementSibling as HTMLElement
                                    if (fallback) fallback.style.display = 'flex'
                                  }}
                                />
                              ) : null}
                              <div
                                className="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-blue-300"
                                style={{ display: match.homeTeam.crest ? 'none' : 'flex' }}
                              >
                                {match.homeTeam.shortName.substring(0, 1).toUpperCase()}
                              </div>
                            </div>
                            <span className="font-medium text-white text-sm truncate">{match.homeTeam.name}</span>
                          </div>

                          <span className="text-slate-400 font-bold mx-3 text-xs">VS</span>

                          <div className="flex items-center gap-2 flex-1 min-w-0 justify-end">
                            <span className="font-medium text-white text-sm truncate">{match.awayTeam.name}</span>
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {match.awayTeam.crest ? (
                                <img
                                  src={match.awayTeam.crest}
                                  alt={match.awayTeam.name}
                                  className="w-6 h-6 object-contain"
                                  onError={(e) => {
                                    const target = e.currentTarget as HTMLImageElement
                                    target.style.display = 'none'
                                    const fallback = target.nextElementSibling as HTMLElement
                                    if (fallback) fallback.style.display = 'flex'
                                  }}
                                />
                              ) : null}
                              <div
                                className="w-6 h-6 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-red-300"
                                style={{ display: match.awayTeam.crest ? 'none' : 'flex' }}
                              >
                                {match.awayTeam.shortName.substring(0, 1).toUpperCase()}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Data/Hora */}
                        <div className="text-sm text-slate-400 text-center mb-4 font-medium">
                          {date} - {time}
                        </div>

                        {/* Botões de Aposta Mobile */}
                        <div className="grid grid-cols-3 gap-2">
                          <Button
                            size="sm"
                            variant={apostaAtual === "casa" ? "default" : "outline"}
                            className={`h-16 text-xs font-bold transition-all duration-200 mobile-friendly ${
                              apostaAtual === "casa"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "casa")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-8 h-8 flex items-center justify-center">
                                {match.homeTeam.crest ? (
                                  <img
                                    src={match.homeTeam.crest}
                                    alt={match.homeTeam.name}
                                    className="w-8 h-8 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-blue-300"
                                  style={{ display: match.homeTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.homeTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                              <span className="text-xs font-medium leading-tight text-center">Casa</span>
                            </div>
                          </Button>

                          <Button
                            size="sm"
                            variant={apostaAtual === "empate" ? "default" : "outline"}
                            className={`h-16 text-xs font-bold transition-all duration-200 mobile-friendly ${
                              apostaAtual === "empate"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "empate")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <span className="text-xl font-black">=</span>
                              <span className="text-xs font-bold">Empate</span>
                            </div>
                          </Button>

                          <Button
                            size="sm"
                            variant={apostaAtual === "fora" ? "default" : "outline"}
                            className={`h-16 text-xs font-bold transition-all duration-200 mobile-friendly ${
                              apostaAtual === "fora"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "fora")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-8 h-8 flex items-center justify-center">
                                {match.awayTeam.crest ? (
                                  <img
                                    src={match.awayTeam.crest}
                                    alt={match.awayTeam.name}
                                    className="w-8 h-8 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-red-300"
                                  style={{ display: match.awayTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.awayTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                              <span className="text-xs font-medium leading-tight text-center">Fora</span>
                            </div>
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}

        {/* Betting Summary */}
        <div className="bg-slate-800 rounded-lg p-3 sm:p-4 mt-4 sm:mt-6">
          {apostasEncerradas ? (
            <div className="text-center">
              <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-4 mb-4">
                <h3 className="text-red-400 font-bold text-lg mb-2">⏰ Apostas Encerradas</h3>
                <p className="text-red-300 text-sm">
                  As apostas foram encerradas pois o primeiro jogo já começou.
                </p>
                {!bolaoFinalizado && (
                  <p className="text-yellow-300 text-sm mt-2">
                    Aguarde o final de todos os jogos para ver o ranking final.
                  </p>
                )}
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-3 sm:mb-4">
                <p className="text-base sm:text-lg font-medium">{getApostasFeitas()}/11 APOSTAS SELECIONADAS</p>
              </div>
              <Button
                onClick={handleFinalizarAposta}
                disabled={!podeFinalizarAposta()}
                className={`w-full py-3 text-sm sm:text-lg font-medium rounded-lg transition-colors ${
                  podeFinalizarAposta()
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-slate-600 text-slate-400 cursor-not-allowed"
                }`}
              >
                {podeFinalizarAposta() ? `FINALIZAR APOSTA - R$ ${valorAposta.toFixed(2)}` : "SELECIONE 11 APOSTAS"}
              </Button>
            </>
          )}
        </div>
      </main>

      {/* Login Dialog */}
      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent className="w-[95vw] max-w-md bg-gradient-to-br from-slate-800 to-slate-900 border-slate-600 shadow-2xl backdrop-blur-sm">
          <DialogHeader>
            <DialogTitle className="text-white text-lg sm:text-xl">Entrar na sua conta</DialogTitle>
            <DialogDescription className="text-slate-400 text-sm sm:text-base">
              Digite suas credenciais para acessar o sistema
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {error && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertDescription className="text-red-400">{error}</AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-white">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={loginForm.email}
                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="senha" className="text-white">
                Senha
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="senha"
                  type={showPassword ? "text" : "password"}
                  placeholder="Sua senha"
                  value={loginForm.senha}
                  onChange={(e) => setLoginForm({ ...loginForm, senha: e.target.value })}
                  className="pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-slate-400 hover:text-white"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleLogin} disabled={loginLoading} className="flex-1 bg-green-600 hover:bg-green-700">
                {loginLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Entrar
              </Button>
              <Button
                onClick={() => {
                  setShowLoginDialog(false)
                  setShowRegisterDialog(true)
                  setError("")
                }}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                Criar conta
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Register Dialog */}
      <Dialog open={showRegisterDialog} onOpenChange={setShowRegisterDialog}>
        <DialogContent className="w-[95vw] max-w-md bg-gradient-to-br from-slate-800 to-slate-900 border-slate-600 shadow-2xl backdrop-blur-sm max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white text-lg sm:text-xl">Criar nova conta</DialogTitle>
            <DialogDescription className="text-slate-400">
              {codigoAfiliado ? (
                <div className="bg-green-600/20 border border-green-600/30 rounded-lg p-3 mt-2">
                  <div className="flex items-center gap-2 text-green-400">
                    <UserPlus className="h-4 w-4" />
                    <span className="font-medium">Indicação de Afiliado</span>
                  </div>
                  <p className="text-sm text-green-300 mt-1">
                    Você está se registrando através do link de um afiliado!
                    Ao criar sua conta, você e o afiliado receberão benefícios especiais.
                  </p>
                  <p className="text-xs text-green-400 mt-1">
                    Código: <span className="font-mono">{codigoAfiliado}</span>
                  </p>
                </div>
              ) : (
                "Preencha os dados para criar sua conta"
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {error && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertDescription className="text-red-400">{error}</AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="nome" className="text-white">
                Nome completo
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="nome"
                  type="text"
                  placeholder="Seu nome completo"
                  value={registerForm.nome}
                  onChange={(e) => setRegisterForm({ ...registerForm, nome: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-email" className="text-white">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="register-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={registerForm.email}
                  onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="telefone" className="text-white">
                Telefone
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="telefone"
                  type="tel"
                  placeholder="(11) 99999-9999"
                  value={registerForm.telefone}
                  onChange={(e) => setRegisterForm({ ...registerForm, telefone: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="cpf" className="text-white">
                CPF
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="cpf"
                  type="text"
                  placeholder="000.000.000-00"
                  value={registerForm.cpf}
                  onChange={(e) => handleCPFChange(e.target.value)}
                  className={`pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 transition-all duration-200 ${
                    cpfError ? "focus:ring-red-500 border-red-500" : "focus:ring-green-500"
                  }`}
                  maxLength={14}
                />
                {showCPFValidation && (
                  <div className="absolute right-3 top-3">
                    {isCPFValid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                )}
              </div>
              {cpfError && <p className="text-red-400 text-sm mt-1">{cpfError}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-senha" className="text-white">
                Senha
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="register-senha"
                  type={showPassword ? "text" : "password"}
                  placeholder="Mínimo 6 caracteres"
                  value={registerForm.senha}
                  onChange={(e) => setRegisterForm({ ...registerForm, senha: e.target.value })}
                  className="pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-slate-400 hover:text-white"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmar-senha" className="text-white">
                Confirmar senha
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="confirmar-senha"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirme sua senha"
                  value={registerForm.confirmarSenha}
                  onChange={(e) => setRegisterForm({ ...registerForm, confirmarSenha: e.target.value })}
                  className="pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-3 text-slate-400 hover:text-white"
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleRegister}
                disabled={registerLoading || !!cpfError}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:opacity-50"
              >
                {registerLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Criar conta
              </Button>
              <Button
                onClick={() => {
                  setShowRegisterDialog(false)
                  setShowLoginDialog(true)
                  setError("")
                  setCpfError("")
                }}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                Já tenho conta
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* User Bilhetes Dialog */}
      <Dialog open={showUserBilhetes} onOpenChange={setShowUserBilhetes}>
        <DialogContent className="w-[95vw] max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Receipt className="h-5 w-5" />
              Meus Bilhetes
            </DialogTitle>
            <DialogDescription>Histórico de todas as suas apostas</DialogDescription>
          </DialogHeader>
          <div className="space-y-3">
            {userBilhetes.length === 0 ? (
              <div className="text-center py-8">
                <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Você ainda não fez nenhuma aposta</p>
              </div>
            ) : (
              userBilhetes.map((bilhete) => (
                <Card
                  key={bilhete.id}
                  className="cursor-pointer hover:shadow-md transition-all duration-200 border-l-4 border-l-blue-500"
                  onClick={() => handleBilheteClick(bilhete)}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium text-gray-900">Bilhete #{bilhete.id}</h3>
                        <p className="text-gray-600 text-sm flex items-center gap-1 mt-1">
                          <Calendar className="h-4 w-4" />
                          {bilhete.data} - {bilhete.hora}
                        </p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(bilhete.status)}
                        <div className="font-medium mt-1 flex items-center gap-1 text-gray-900">
                          <DollarSign className="h-4 w-4" />
                          R$ {bilhete.valor.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* User Pagamentos Dialog */}
      <Dialog open={showUserPagamentos} onOpenChange={setShowUserPagamentos}>
        <DialogContent className="w-[95vw] max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Meus Pagamentos
            </DialogTitle>
            <DialogDescription>Histórico de todos os seus pagamentos</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {userPagamentos.length === 0 ? (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Você ainda não fez nenhum pagamento</p>
              </div>
            ) : (
              userPagamentos.map((pagamento) => (
                <Card key={pagamento.id} className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-gray-900">Pagamento #{pagamento.id}</span>
                          {getStatusBadge(pagamento.status)}
                        </div>
                        <div className="text-gray-600 text-sm flex items-center gap-2 mb-1">
                          <Calendar className="h-4 w-4" />
                          {pagamento.data}
                        </div>
                        <div className="text-gray-600 text-sm mb-2">
                          <strong>Bilhete:</strong> {pagamento.bilhete_id}
                        </div>
                        <div className="text-gray-600 text-sm">
                          <strong>Método:</strong> {pagamento.metodo}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-lg flex items-center gap-1 mb-1 text-gray-900">
                          <DollarSign className="h-5 w-5" />
                          R$ {pagamento.valor.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {pagamento.status === "aprovado" ? "✅ Confirmado" : "⏳ Pendente"}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* User Profile Dialog */}
      <Dialog open={showUserProfile} onOpenChange={setShowUserProfile}>
        <DialogContent className="w-[95vw] max-w-4xl max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Meu Perfil
            </DialogTitle>
            <DialogDescription>
              Informações completas da sua conta e histórico de apostas
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Informações Pessoais */}
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Informações Pessoais
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-600">Nome</Label>
                  <p className="font-medium text-gray-900">{user?.nome}</p>
                </div>
                <div>
                  <Label className="text-gray-600">Email</Label>
                  <p className="font-medium text-gray-900">{user?.email}</p>
                </div>
                {user?.telefone && (
                  <div>
                    <Label className="text-gray-600">Telefone</Label>
                    <p className="font-medium text-gray-900">{user.telefone}</p>
                  </div>
                )}
                {user?.cpf && (
                  <div>
                    <Label className="text-gray-600">CPF</Label>
                    <p className="font-medium text-gray-900">{user.cpf}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Estatísticas Gerais */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="border-l-4 border-l-blue-500">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-1">{userStats.totalApostas}</div>
                  <div className="text-gray-600 text-sm">Total de Apostas</div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-red-500">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-red-600 mb-1 flex items-center justify-center gap-1">
                    <DollarSign className="h-5 w-5" />
                    {userStats.totalGasto.toFixed(2)}
                  </div>
                  <div className="text-gray-600 text-sm">Total Investido</div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600 mb-1 flex items-center justify-center gap-1">
                    <DollarSign className="h-5 w-5" />
                    {userStats.totalGanho.toFixed(2)}
                  </div>
                  <div className="text-gray-600 text-sm">Total Ganho</div>
                </CardContent>
              </Card>
            </div>

            {/* Ações Rápidas */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                className="flex-1"
                onClick={() => {
                  setShowUserProfile(false)
                  window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })
                }}
              >
                <DollarSign className="mr-2 h-4 w-4" />
                Nova Aposta
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  setShowUserProfile(false)
                  setShowUserBilhetes(true)
                }}
              >
                <Receipt className="mr-2 h-4 w-4" />
                Ver todos os bilhetes ({userBilhetes.length})
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  setShowUserProfile(false)
                  setShowUserPagamentos(true)
                }}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Ver Pagamentos
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* PIX Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="w-[95vw] max-w-[400px] p-4 max-h-[90vh] overflow-hidden">
          <DialogHeader className="text-center pb-3">
            {!paymentLoading && (
              <DialogDescription className="text-white/90 text-sm">
                Escaneie o QR Code para pagar sua aposta
              </DialogDescription>
            )}
          </DialogHeader>
          <div className="space-y-3">
            {paymentLoading ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-green-200 border-t-green-500 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 bg-green-500 rounded-full animate-pulse"></div>
                  </div>
                </div>
                <p className="text-white text-base font-medium mt-4">Gerando QR Code PIX...</p>
                <p className="text-white/70 text-sm mt-1">Aguarde um momento</p>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-8 space-y-4">
                <div className="text-red-400 text-4xl">⚠️</div>
                <p className="text-white text-center text-sm">{error}</p>
                <Button
                  onClick={() => {
                    setError("")
                    generatePixQRCode()
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg"
                >
                  🔄 Tentar Novamente
                </Button>
              </div>
            ) : pixData ? (
              <>
                {/* QR Code */}
                <div className="flex justify-center items-center">
                  <div className="bg-white p-4 rounded-2xl shadow-lg border-4 border-blue-500"
                       style={{
                         background: 'linear-gradient(145deg, #ffffff, #f0f9ff)',
                         boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.5)'
                       }}>
                    {(() => {
                      console.log('🔍 PIX Data Debug COMPLETO:', {
                        pixData: pixData,
                        hasQrcodeImage: !!pixData.qrcode_image,
                        hasQrCodeValue: !!pixData.qr_code_value,
                        qrCodeValueLength: pixData.qr_code_value?.length,
                        qrCodeValueStart: pixData.qr_code_value?.substring(0, 100),
                        qrCodeValueFull: pixData.qr_code_value
                      })
                      return null
                    })()}

                    {pixData.qr_code_value ? (
                      <QRCodeComponent
                        value={pixData.qr_code_value}
                        size={240}
                        className="rounded-xl"
                      />
                    ) : pixData.qrcode_image ? (
                      <img
                        src={pixData.qrcode_image}
                        alt="QR Code PIX"
                        className="w-60 h-60 object-contain rounded-xl"
                        onLoad={() => console.log('✅ Imagem QR Code carregada')}
                        onError={() => console.log('❌ Erro ao carregar imagem QR Code')}
                      />
                    ) : (
                      <div className="w-60 h-60 bg-gray-100 flex items-center justify-center rounded-xl">
                        <span className="text-gray-500 text-sm text-center">QR Code<br/>indisponível</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* PIX Code - Compacto */}
                <div className="bg-white border border-gray-200 p-2 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="text-xs text-gray-500 font-mono">
                        {pixCodeCopied ? (
                          <div className="text-green-600 font-medium">
                            Código copiado para pagamento!
                          </div>
                        ) : (
                          <div className="truncate">
                            {pixData.qr_code_value.substring(0, 50)}...
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      onClick={copyPixCode}
                      size="sm"
                      className={`${pixCodeCopied
                        ? 'bg-green-600 hover:bg-green-700'
                        : 'bg-blue-600 hover:bg-blue-700'
                      } text-white text-xs px-3 py-2 h-auto rounded-md transition-all duration-300 flex-shrink-0 font-medium`}
                    >
                      {pixCodeCopied ? '✅ Copiado!' : '📋 Copiar'}
                    </Button>
                  </div>
                </div>

                {/* Status */}
                {pixData.status === "confirmed" ? (
                  <Button
                    onClick={() => {
                      setShowPaymentDialog(false)
                      setShowBilheteDialog(true)
                    }}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 text-sm rounded-lg"
                  >
                    🎫 Ver Bilhete e Imprimir
                  </Button>
                ) : (
                  <div className="bg-yellow-600/20 border border-yellow-600/50 rounded-lg p-3">
                    <div className="flex items-center justify-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin text-yellow-400" />
                      <span className="text-yellow-400 text-sm font-medium">
                        Aguardando confirmação do pagamento...
                      </span>
                    </div>
                  </div>
                )}
              </>
            ) : null}
          </div>
        </DialogContent>
      </Dialog>

      {/* Bilhete Dialog */}
      <Dialog open={showBilheteDialog} onOpenChange={setShowBilheteDialog}>
        <DialogContent className="w-[95vw] max-w-md max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-gray-900">Bilhete de Aposta</DialogTitle>
            <DialogDescription className="text-gray-600">
              {selectedBilhete ? "Detalhes do bilhete" : "Seu bilhete foi gerado com sucesso!"}
            </DialogDescription>
          </DialogHeader>
          {(bilhete || selectedBilhete) && (
            <div className="space-y-4">
              <div className="bg-white border border-gray-200 p-4 rounded-lg space-y-2 shadow-sm">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Bilhete:</span>
                  <span className="text-gray-900 font-mono">{(selectedBilhete || bilhete)?.id}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Data/Hora:</span>
                  <span className="text-gray-900">
                    {(selectedBilhete || bilhete)?.data} - {(selectedBilhete || bilhete)?.hora}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Valor:</span>
                  <span className="text-gray-900">R$ {(selectedBilhete || bilhete)?.valor.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Status:</span>
                  <span className="text-gray-900">
                    {getStatusBadge((selectedBilhete || bilhete)?.status || "pendente")}
                  </span>
                </div>
                {(selectedBilhete || bilhete)?.qr_code_pix && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <span className="text-gray-600 text-sm">QR Code PIX:</span>
                    <div className="bg-gray-100 p-2 rounded mt-1 border border-gray-200">
                      <p className="text-xs text-gray-700 font-mono break-all">
                        {(selectedBilhete || bilhete)?.qr_code_pix}
                      </p>
                    </div>
                  </div>
                )}
                {(selectedBilhete || bilhete)?.transaction_id && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">ID Transação:</span>
                    <span className="text-gray-900 font-mono text-xs">{(selectedBilhete || bilhete)?.transaction_id}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <h4 className="text-white font-medium">Apostas:</h4>
                <div className="bg-white border border-gray-200 p-3 rounded-lg space-y-1 shadow-sm">
                  {(selectedBilhete || bilhete)?.apostas && (selectedBilhete || bilhete)?.apostas?.length > 0 ? (
                    (selectedBilhete || bilhete)?.apostas?.map((aposta: any, index: number) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-gray-700">{aposta.jogo}</span>
                        <span className="text-gray-900 font-medium">{aposta.resultado}</span>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-600 text-sm">Nenhuma aposta encontrada neste bilhete</p>
                      <p className="text-gray-500 text-xs mt-1">
                        Bilhete ID: {(selectedBilhete || bilhete)?.id}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Botão de imprimir sempre disponível para bilhetes pagos */}
              {((selectedBilhete && selectedBilhete.status === 'pago') ||
                (!selectedBilhete && bilhete && (bilhete.status === 'pago' || pixData?.status === 'confirmed'))) && (
                <Button
                  onClick={printBilhete}
                  disabled={printingBilhete}
                  className="w-full bg-green-600 hover:bg-green-700 py-3 text-base font-medium"
                >
                  {printingBilhete ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Imprimindo bilhete...
                    </>
                  ) : (
                    <>
                      <Printer className="h-4 w-4 mr-2" />
                      🖨️ Imprimir Bilhete
                    </>
                  )}
                </Button>
              )}

              {/* Mensagem para bilhetes pendentes */}
              {((selectedBilhete && selectedBilhete.status !== 'pago') ||
                (!selectedBilhete && bilhete && bilhete.status !== 'pago' && pixData?.status !== 'confirmed')) && (
                <div className="bg-yellow-600/20 border border-yellow-600/50 rounded-lg p-3 space-y-3">
                  <div className="text-center">
                    <span className="text-yellow-400 text-sm">
                      ⏳ Aguardando confirmação do pagamento para imprimir
                    </span>
                  </div>

                  {/* Botão para confirmar pagamento manualmente */}
                  <div className="text-center">
                    <Button
                      onClick={async () => {
                        const bilheteParaConfirmar = selectedBilhete || bilhete
                        if (!bilheteParaConfirmar || !user?.id) return

                        try {
                          // Primeiro verificar se pode confirmar manualmente
                          const checkResponse = await fetch('/api/payment/check-status', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              bilhete_codigo: bilheteParaConfirmar.codigo,
                              user_id: user.id
                            })
                          })

                          if (checkResponse.ok) {
                            const checkResult = await checkResponse.json()

                            if (checkResult.already_paid) {
                              toast.success("✅ Este bilhete já está pago!")
                              // Atualizar estado local
                              if (selectedBilhete) {
                                setSelectedBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }
                              if (bilhete) {
                                setBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }
                              return
                            }

                            if (!checkResult.can_confirm_manually) {
                              toast.error("⏳ Aguarde mais alguns minutos antes de confirmar manualmente")
                              return
                            }
                          }

                          // Confirmar se o usuário realmente pagou
                          const confirmacao = confirm(
                            `⚠️ CONFIRMAÇÃO DE PAGAMENTO\n\n` +
                            `Bilhete: ${bilheteParaConfirmar.codigo}\n` +
                            `Valor: R$ ${bilheteParaConfirmar.valor_total || bilheteParaConfirmar.valor}\n\n` +
                            `Você REALMENTE já pagou este bilhete via PIX?\n\n` +
                            `⚠️ Use esta opção apenas se já pagou e o sistema não detectou automaticamente.`
                          )

                          if (!confirmacao) {
                            return
                          }

                          // Simular webhook de pagamento aprovado
                          const response = await fetch('/api/v1/MP/webhookruntransation', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              order_id: bilheteParaConfirmar.codigo,
                              status: 'PAID',
                              type: 'PIXOUT',
                              message: 'Payment confirmed manually by user'
                            })
                          })

                          if (response.ok) {
                            const result = await response.json()
                            if (result.status === 'pago') {
                              // Atualizar estado local
                              if (selectedBilhete) {
                                setSelectedBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }
                              if (bilhete) {
                                setBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }

                              // Atualizar lista de bilhetes do usuário
                              setUserBilhetes(prev =>
                                prev.map(b =>
                                  b.codigo === bilheteParaConfirmar.codigo
                                    ? { ...b, status: 'pago' }
                                    : b
                                )
                              )

                              // Mostrar notificação de sucesso
                              playSuccessSound()
                              setPaymentSuccessData({
                                codigo: bilheteParaConfirmar.codigo,
                                valor: bilheteParaConfirmar.valor_total || bilheteParaConfirmar.valor || "0,00",
                                data: new Date().toLocaleString('pt-BR'),
                                transactionId: bilheteParaConfirmar.transaction_id || bilheteParaConfirmar.codigo
                              })
                              setShowPaymentSuccess(true)

                              // Mostrar SweetAlert2 de sucesso
                              showPaymentSuccessAlert({
                                codigo: bilheteParaConfirmar.codigo || "N/A",
                                valor: bilheteParaConfirmar.valor_total || bilheteParaConfirmar.valor || "0,00",
                                clientName: user?.nome || "Cliente",
                                transactionId: bilheteParaConfirmar.transaction_id || bilheteParaConfirmar.codigo || "N/A"
                              })

                              toast.success("🎉 Pagamento confirmado manualmente!")
                            } else {
                              toast.error("❌ Erro ao confirmar pagamento")
                            }
                          } else {
                            toast.error("❌ Erro na comunicação com o servidor")
                          }
                        } catch (error) {
                          console.error("Erro ao confirmar pagamento:", error)
                          toast.error("❌ Erro ao confirmar pagamento")
                        }
                      }}
                      variant="outline"
                      size="sm"
                      className="border-yellow-500 text-yellow-400 hover:bg-yellow-500/10 text-xs"
                    >
                      ✅ Já Paguei - Confirmar Manualmente
                    </Button>
                  </div>

                  <div className="text-center text-xs text-yellow-500">
                    Use apenas se já pagou e o sistema não detectou automaticamente
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Payment Success Dialog */}
      {showPaymentSuccess && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
          <div className="w-[90vw] max-w-sm sm:max-w-md bg-gradient-to-br from-green-600 to-green-700 border border-green-500 text-white rounded-2xl shadow-2xl relative animate-in fade-in-0 zoom-in-95 duration-300">
            {/* Botão de fechar */}
            <button
              onClick={() => setShowPaymentSuccess(false)}
              className="absolute top-4 right-4 text-white/70 hover:text-white text-xl z-10 w-8 h-8 flex items-center justify-center rounded-full hover:bg-white/10 transition-colors"
            >
              ✕
            </button>
          {/* Animação de confete */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="confetti-animation absolute top-2 left-1/4 w-2 h-2 bg-yellow-300 rounded-full" style={{animationDelay: '0s'}}></div>
            <div className="confetti-animation absolute top-2 right-1/4 w-2 h-2 bg-blue-300 rounded-full" style={{animationDelay: '0.2s'}}></div>
            <div className="confetti-animation absolute top-2 left-1/2 w-2 h-2 bg-red-300 rounded-full" style={{animationDelay: '0.4s'}}></div>
            <div className="confetti-animation absolute top-2 left-3/4 w-2 h-2 bg-purple-300 rounded-full" style={{animationDelay: '0.6s'}}></div>
            <div className="confetti-animation absolute top-2 right-1/3 w-2 h-2 bg-pink-300 rounded-full" style={{animationDelay: '0.8s'}}></div>
          </div>

          <div className="text-center relative z-10 pt-6 pb-4">
            <div className="success-icon mx-auto mb-4 w-16 h-16 sm:w-20 sm:h-20 bg-white rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl sm:text-4xl animate-bounce">✅</span>
            </div>
            <h2 className="success-title text-xl sm:text-2xl font-bold text-white mb-2">
              PAGO COM SUCESSO!
            </h2>
            <p className="text-green-100 text-base sm:text-lg font-medium">
              Seu bilhete foi aprovado
            </p>
          </div>

          <div className="space-y-4 sm:space-y-6 py-4 px-2 sm:px-4 relative z-10">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6 space-y-3 border border-white/20">
              <div className="payment-info-item flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2">
                <span className="payment-info-label text-green-100 text-sm sm:text-base font-medium">Código do Bilhete:</span>
                <span className="payment-info-value font-bold text-white font-mono text-sm sm:text-base break-all">
                  {paymentSuccessData?.codigo}
                </span>
              </div>
              <div className="payment-info-item flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2">
                <span className="payment-info-label text-green-100 text-sm sm:text-base font-medium">Valor Pago:</span>
                <span className="payment-info-value font-bold text-white text-lg sm:text-xl">
                  R$ {paymentSuccessData?.valor}
                </span>
              </div>
              <div className="payment-info-item flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2">
                <span className="payment-info-label text-green-100 text-sm sm:text-base font-medium">Data/Hora:</span>
                <span className="payment-info-value font-bold text-white text-sm sm:text-base">
                  {paymentSuccessData?.data}
                </span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6 text-center border border-white/20">
              <div className="text-green-100 text-sm sm:text-base mb-3 font-medium">Status do Bilhete</div>
              <div className="status-badge inline-flex items-center px-4 py-2 sm:px-6 sm:py-3 rounded-full bg-green-500 text-white font-bold text-sm sm:text-base shadow-lg">
                ✅ APROVADO
              </div>
            </div>

            <div className="text-center text-green-100 text-sm sm:text-base font-medium">
              🎯 Boa sorte em suas apostas!
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-4 px-2 sm:px-4">
            <Button
              onClick={() => {
                setShowPaymentSuccess(false)
                setShowUserBilhetes(true)
              }}
              className="success-button flex-1 bg-white text-green-600 hover:bg-green-50 font-bold py-3 text-sm sm:text-base rounded-xl shadow-lg transition-all duration-200 hover:scale-105"
            >
              📋 Ver Meus Bilhetes
            </Button>
            <button
              onClick={() => setShowPaymentSuccess(false)}
              className="success-button flex-1 bg-white text-green-700 hover:bg-green-50 font-bold py-3 text-sm sm:text-base rounded-xl transition-all duration-200 hover:scale-105 shadow-lg"
            >
              ✅ OK
            </button>
          </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}
