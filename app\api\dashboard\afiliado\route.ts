import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const userId = searchParams.get('userId') || '1' // Por enquanto usar usuário padrão

    // Buscar dados do afiliado
    const afiliados = await executeQuery(`
      SELECT 
        a.id,
        a.codigo_afiliado,
        a.percentual_comissao,
        a.cpa_valor,
        a.tipo_comissao,
        a.status,
        a.data_cadastro,
        u.nome,
        u.email,
        u.telefone
      FROM afiliados a
      JOIN usuarios u ON a.usuario_id = u.id
      WHERE a.usuario_id = ?
    `, [userId])

    if (afiliados.length === 0) {
      return NextResponse.json({ afiliado: null })
    }

    const afiliado = afiliados[0]

    // Buscar estatísticas do afiliado
    const stats = await executeQuery(`
      SELECT 
        COUNT(DISTINCT u.id) as total_indicacoes,
        COALESCE(SUM(c.valor), 0) as comissao_total,
        COALESCE(SUM(CASE WHEN DATE(c.data_comissao) = CURDATE() THEN c.valor ELSE 0 END), 0) as comissao_hoje
      FROM usuarios u
      LEFT JOIN comissoes c ON u.id = c.usuario_indicado_id
      WHERE u.afiliado_id = ?
    `, [afiliado.id])

    const estatisticas = stats[0] || {
      total_indicacoes: 0,
      comissao_total: 0,
      comissao_hoje: 0
    }

    // Buscar indicações recentes
    const indicacoes = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.data_cadastro,
        u.status,
        COALESCE(SUM(a.valor_total), 0) as total_apostado
      FROM usuarios u
      LEFT JOIN apostas a ON u.id = a.usuario_id
      WHERE u.afiliado_id = ?
      GROUP BY u.id, u.nome, u.email, u.data_cadastro, u.status
      ORDER BY u.data_cadastro DESC
      LIMIT 10
    `, [afiliado.id])

    // Buscar histórico de comissões
    const comissoes = await executeQuery(`
      SELECT 
        c.id,
        c.valor,
        c.tipo,
        c.data_comissao as data,
        c.status,
        u.nome as usuario_nome,
        a.codigo_bilhete
      FROM comissoes c
      JOIN usuarios u ON c.usuario_indicado_id = u.id
      LEFT JOIN apostas a ON c.aposta_id = a.id
      WHERE c.afiliado_id = ?
      ORDER BY c.data_comissao DESC
      LIMIT 20
    `, [afiliado.id])

    const afiliadoData = {
      id: afiliado.id,
      nome: afiliado.nome,
      email: afiliado.email,
      telefone: afiliado.telefone,
      codigo_afiliado: afiliado.codigo_afiliado,
      percentual_comissao: parseFloat(afiliado.percentual_comissao) || 0,
      cpa_valor: parseFloat(afiliado.cpa_valor) || 0,
      tipo_comissao: afiliado.tipo_comissao || 'percentual',
      status: afiliado.status,
      data_cadastro: afiliado.data_cadastro,
      estatisticas: {
        total_indicacoes: parseInt(estatisticas.total_indicacoes) || 0,
        comissao_total: parseFloat(estatisticas.comissao_total) || 0,
        comissao_hoje: parseFloat(estatisticas.comissao_hoje) || 0
      },
      indicacoes: indicacoes.map((ind: any) => ({
        id: ind.id,
        nome: ind.nome,
        email: ind.email,
        data_cadastro: ind.data_cadastro,
        status: ind.status,
        total_apostado: parseFloat(ind.total_apostado) || 0
      })),
      comissoes: comissoes.map((com: any) => ({
        id: com.id,
        valor: parseFloat(com.valor) || 0,
        tipo: com.tipo,
        data: com.data,
        status: com.status,
        usuario_nome: com.usuario_nome,
        bilhete_codigo: com.codigo_bilhete
      }))
    }

    return NextResponse.json({ afiliado: afiliadoData })

  } catch (error) {
    console.error('❌ Erro ao buscar dados do afiliado:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId = 1 } = body

    // Verificar se já é afiliado
    const existingAfiliado = await executeQuery(`
      SELECT id FROM afiliados WHERE usuario_id = ?
    `, [userId])

    if (existingAfiliado.length > 0) {
      return NextResponse.json(
        { error: 'Usuário já é afiliado' },
        { status: 400 }
      )
    }

    // Gerar código único do afiliado
    const codigoAfiliado = `AF${Date.now().toString().slice(-6)}`

    // Criar afiliado
    const result = await executeQuery(`
      INSERT INTO afiliados (
        usuario_id,
        codigo_afiliado,
        percentual_comissao,
        tipo_comissao,
        status,
        data_cadastro
      ) VALUES (?, ?, 10.0, 'percentual', 'ativo', NOW())
    `, [userId, codigoAfiliado])

    const afiliadoId = (result as any).insertId

    return NextResponse.json({
      success: true,
      afiliado_id: afiliadoId,
      codigo_afiliado: codigoAfiliado,
      message: 'Afiliado criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar afiliado:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
