"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Trophy,
  Users,
  DollarSign,
  Target,
  BarChart3,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  UserCheck,
  Loader2,
  RefreshCw,
} from "lucide-react"
import { toast } from "sonner"

interface DashboardStats {
  totalBoloes: number
  totalUsuarios: number
  faturamentoMes: number
  apostasHoje: number
  cambistasAtivos: number
  jogosHoje: number
}

interface RecentActivity {
  id: number
  type: "bet" | "payment" | "win" | "user"
  user: string
  amount?: number
  description: string
  time: string
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalBoloes: 0,
    totalUsuarios: 0,
    faturamentoMes: 0,
    apostasHoje: 0,
    cambistasAtivos: 0,
    jogosHoje: 0,
  })
  const [loading, setLoading] = useState(true)
  const [syncLoading, setSyncLoading] = useState(false)

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const [dashboardResponse, activitiesResponse] = await Promise.all([
        fetch("/api/admin/dashboard"),
        fetch("/api/admin/activities")
      ])

      if (!dashboardResponse.ok) {
        throw new Error(`HTTP error! status: ${dashboardResponse.status}`)
      }

      const dashboardData = await dashboardResponse.json()

      if (dashboardData.success) {
        setStats(dashboardData.stats)
      } else {
        console.error("Erro ao carregar dados do dashboard:", dashboardData.message)
      }

      // Carregar atividades se a API estiver funcionando
      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json()
        if (activitiesData.success) {
          setRecentActivity(activitiesData.activities)
        }
      }
    } catch (error) {
      console.error("Erro ao carregar dados do dashboard:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])

  const syncFootballAPI = async () => {
    try {
      setSyncLoading(true)
      toast.info("🔄 Iniciando sincronização do sistema...")

      // Chamar API de sincronização
      const response = await fetch('/api/admin/sync-system', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error('Erro na sincronização')
      }

      const data = await response.json()

      if (data.success) {
        toast.success(`✅ Sistema sincronizado com sucesso!`)
        toast.success(`📊 ${data.data.campeonatos_sincronizados} campeonatos sincronizados`)
        toast.success(`⚽ ${data.data.partidas_sincronizadas} partidas sincronizadas`)

        // Recarregar dados do dashboard
        fetchDashboardData()
      } else {
        throw new Error(data.message || 'Erro na sincronização')
      }

    } catch (error: any) {
      console.error("❌ Erro na sincronização:", error)
      toast.error(`❌ ${error.message || 'Erro ao sincronizar dados'}`)
    } finally {
      setSyncLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "bet":
        return <Target className="h-4 w-4 text-blue-500" />
      case "payment":
        return <DollarSign className="h-4 w-4 text-green-500" />
      case "win":
        return <Trophy className="h-4 w-4 text-yellow-500" />
      case "user":
        return <Users className="h-4 w-4 text-purple-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case "bet":
        return "bg-blue-50 border-blue-200"
      case "payment":
        return "bg-green-50 border-green-200"
      case "win":
        return "bg-yellow-50 border-yellow-200"
      case "user":
        return "bg-purple-50 border-purple-200"
      default:
        return "bg-gray-50 border-gray-200"
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Visão geral do sistema de bolões</p>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Bolões</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalBoloes}</p>
                <div className="flex items-center mt-2">
                  <Trophy className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-blue-600 ml-1">Bolões ativos</span>
                </div>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Trophy className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Usuários Ativos</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalUsuarios.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <ArrowUpRight className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600 ml-1">Usuários ativos</span>
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Faturamento Mensal</p>
                <p className="text-3xl font-bold text-gray-900">{formatCurrency(stats.faturamentoMes)}</p>
                <div className="flex items-center mt-2">
                  <DollarSign className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-orange-600 ml-1">Faturamento mensal</span>
                </div>
              </div>
              <div className="bg-orange-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Apostas Hoje</p>
                <p className="text-3xl font-bold text-gray-900">{stats.apostasHoje}</p>
                <div className="flex items-center mt-2">
                  <Target className="h-4 w-4 text-purple-500" />
                  <span className="text-sm text-purple-600 ml-1">Apostas de hoje</span>
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Cambistas Ativos</p>
                <p className="text-2xl font-bold text-gray-900">{stats.cambistasAtivos}</p>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {stats.cambistasAtivos} online
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Jogos Hoje</p>
                <p className="text-2xl font-bold text-gray-900">{stats.jogosHoje}</p>
              </div>
              <Badge variant="default">{stats.jogosHoje} jogos</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Sistema</p>
                <p className="text-2xl font-bold text-gray-900">Online</p>
              </div>
              <Badge variant="secondary">Funcionando</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Atividade Recente
            </CardTitle>
            <CardDescription>Últimas atividades do sistema</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.length === 0 ? (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Nenhuma atividade recente</p>
                  <p className="text-sm text-gray-500">As atividades aparecerão aqui conforme o uso do sistema</p>
                </div>
              ) : (
                recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className={`flex items-center justify-between p-3 rounded-lg border ${getActivityColor(activity.type)}`}
                  >
                    <div className="flex items-center space-x-3">
                      {getActivityIcon(activity.type)}
                      <div>
                        <p className="text-sm font-medium">{activity.description}</p>
                        <p className="text-xs text-gray-600">{activity.user}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      {activity.amount && <p className="text-sm font-medium">{formatCurrency(activity.amount)}</p>}
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <RefreshCw className="h-5 w-5 mr-2" />
              Atualizar Dados do Sistema
            </CardTitle>
            <CardDescription>Verificar e atualizar dados de campeonatos e partidas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <Button
                onClick={syncFootballAPI}
                disabled={syncLoading}
                className="w-full h-16 text-lg bg-orange-600 hover:bg-orange-700"
              >
                {syncLoading ? (
                  <>
                    <Loader2 className="h-6 w-6 mr-3 animate-spin" />
                    Atualizando dados...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-6 w-6 mr-3" />
                    Atualizar Dados
                  </>
                )}
              </Button>
              <p className="text-sm text-gray-600 mt-3">
                Clique para verificar e atualizar campeonatos e partidas disponíveis
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
