'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Crown, Trophy, Medal, Star, TrendingUp } from 'lucide-react'
import { toast } from 'sonner'

interface RankingUser {
  id: number
  nome: string
  email: string
  pontos_totais: number
  apostas_certas: number
  apostas_totais: number
  percentual_acerto: number
  posicao: number
}

export default function RankPage() {
  const [ranking, setRanking] = useState<RankingUser[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchRanking()
  }, [])

  const fetchRanking = async () => {
    try {
      setLoading(true)

      // Buscar dados reais do ranking
      const response = await fetch('/api/admin/ranking')
      if (!response.ok) {
        throw new Error('Erro ao carregar ranking')
      }

      const data = await response.json()

      if (data.success) {
        // Mapear dados da API para o formato esperado pelo componente
        const rankingFormatted = data.ranking.map((user: any) => ({
          id: user.id,
          nome: user.nome,
          email: user.email,
          pontos_totais: user.palpites_corretos,
          apostas_certas: user.palpites_corretos,
          apostas_totais: user.total_palpites,
          percentual_acerto: user.percentual_acerto,
          posicao: user.posicao
        }))

        setRanking(rankingFormatted)
      } else {
        throw new Error(data.error || 'Erro ao carregar ranking')
      }
    } catch (error: any) {
      console.error('Erro ao buscar ranking:', error)
      toast.error(error.message || 'Erro ao carregar ranking')
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (posicao: number) => {
    switch (posicao) {
      case 1:
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 2:
        return <Trophy className="h-6 w-6 text-gray-400" />
      case 3:
        return <Medal className="h-6 w-6 text-amber-600" />
      default:
        return <Star className="h-6 w-6 text-blue-500" />
    }
  }

  const getRankColor = (posicao: number) => {
    switch (posicao) {
      case 1:
        return 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200'
      case 2:
        return 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'
      case 3:
        return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200'
      default:
        return 'bg-white border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando ranking...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">🏆 Ranking de Usuários</h1>
          <p className="text-gray-600 mt-2">Classificação dos melhores apostadores</p>
        </div>
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5 text-green-600" />
          <span className="text-sm text-gray-600">Atualizado em tempo real</span>
        </div>
      </div>

      <div className="grid gap-4">
        {ranking.map((user) => (
          <Card key={user.id} className={`transition-all hover:shadow-lg ${getRankColor(user.posicao)}`}>
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    {getRankIcon(user.posicao)}
                    <span className="text-xl sm:text-2xl font-bold text-gray-700">#{user.posicao}</span>
                  </div>

                  <div className="min-w-0 flex-1">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 truncate">{user.nome}</h3>
                    <p className="text-xs sm:text-sm text-gray-600 truncate">{user.email}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between sm:justify-end sm:space-x-6 gap-4">
                  <div className="text-center">
                    <p className="text-lg sm:text-3xl font-bold text-green-600">{user.apostas_certas}/{user.apostas_totais}</p>
                    <p className="text-xs text-gray-500">Acertos</p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg sm:text-2xl font-bold text-blue-600">{user.pontos_totais}</p>
                    <p className="text-xs text-gray-500">Pontos</p>
                  </div>

                  <div className="text-center">
                    <Badge variant={user.percentual_acerto >= 70 ? 'default' : user.percentual_acerto >= 50 ? 'secondary' : 'destructive'}>
                      {user.percentual_acerto}%
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">Taxa</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>📊 Estatísticas Gerais</CardTitle>
          <CardDescription>Resumo do desempenho geral dos usuários</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{ranking.length}</p>
              <p className="text-sm text-gray-600">Usuários Ativos</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">
                {Math.round(ranking.reduce((acc, user) => acc + user.percentual_acerto, 0) / ranking.length)}%
              </p>
              <p className="text-sm text-gray-600">Taxa Média de Acerto</p>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">
                {ranking.reduce((acc, user) => acc + user.pontos_totais, 0)}
              </p>
              <p className="text-sm text-gray-600">Total de Pontos</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">
                {ranking.reduce((acc, user) => acc + user.apostas_totais, 0)}
              </p>
              <p className="text-sm text-gray-600">Total de Apostas</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
