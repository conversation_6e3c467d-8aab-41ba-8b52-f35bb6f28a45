"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Trophy,
  History,
  User,
  Settings,
  LogOut,
  Calendar,
  DollarSign,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface UserData {
  id: number
  nome: string
  email: string
  tipo: string
}

interface MinhaAposta {
  id: number
  bolao: string
  jogos: number
  valor: number
  data: string
  status: "pendente" | "ganha" | "perdida" | "em_andamento"
  premio?: number
}

interface HistoricoTransacao {
  id: number
  tipo: "aposta" | "premio" | "reembolso"
  descricao: string
  valor: number
  data: string
  status: "concluido" | "pendente" | "cancelado"
}

export default function DashboardPage() {
  const [user, setUser] = useState<UserData | null>(null)
  const [minhasApostas, setMinhasApostas] = useState<MinhaAposta[]>([])
  const [historico, setHistorico] = useState<HistoricoTransacao[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Verificar autenticação
    const userData = localStorage.getItem("user")
    if (!userData) {
      router.push("/login")
      return
    }

    const parsedUser = JSON.parse(userData)
    setUser(parsedUser)
    loadUserData()
  }, [router])

  const loadUserData = async () => {
    try {
      // Carregar apostas reais do usuário
      const response = await fetch('/api/user/apostas')
      let mockApostas: MinhaAposta[] = []

      if (response.ok) {
        const data = await response.json()
        mockApostas = data.apostas || []
      }

      const mockHistorico: HistoricoTransacao[] = [
        {
          id: 1,
          tipo: "aposta",
          descricao: "Aposta no Brasileirão 2024",
          valor: -25.0,
          data: "2024-01-18",
          status: "concluido",
        },
        {
          id: 2,
          tipo: "premio",
          descricao: "Prêmio Copa do Brasil",
          valor: 120.0,
          data: "2024-01-16",
          status: "concluido",
        },
        {
          id: 3,
          tipo: "aposta",
          descricao: "Aposta Copa do Brasil",
          valor: -40.0,
          data: "2024-01-15",
          status: "concluido",
        },
        {
          id: 4,
          tipo: "aposta",
          descricao: "Aposta Libertadores 2024",
          valor: -30.0,
          data: "2024-01-10",
          status: "concluido",
        },
      ]

      setMinhasApostas(mockApostas)
      setHistorico(mockHistorico)
    } catch (error) {
      console.error("Erro ao carregar dados:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const handleLogout = () => {
    localStorage.removeItem("user")
    router.push("/")
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ganha":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Ganha
          </Badge>
        )
      case "perdida":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Perdida
          </Badge>
        )
      case "em_andamento":
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Em Andamento
          </Badge>
        )
      case "pendente":
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const calcularEstatisticas = () => {
    const totalApostas = minhasApostas.length
    const apostasGanhas = minhasApostas.filter((a) => a.status === "ganha").length
    const totalInvestido = minhasApostas.reduce((sum, a) => sum + a.valor, 0)
    const totalGanho = minhasApostas.filter((a) => a.premio).reduce((sum, a) => sum + (a.premio || 0), 0)
    const lucro = totalGanho - totalInvestido

    return {
      totalApostas,
      apostasGanhas,
      totalInvestido,
      totalGanho,
      lucro,
      percentualAcerto: totalApostas > 0 ? (apostasGanhas / totalApostas) * 100 : 0,
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando seu dashboard...</p>
        </div>
      </div>
    )
  }

  const stats = calcularEstatisticas()

  return (
    <div className="space-y-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Bem-vindo, {user?.nome}!</h1>
          <p className="text-gray-600">Acompanhe suas apostas e gerencie sua conta</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total de Apostas</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.totalApostas}</p>
                </div>
                <div className="bg-blue-100 p-2 sm:p-3 rounded-full flex-shrink-0">
                  <Target className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Apostas Ganhas</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.apostasGanhas}</p>
                  <p className="text-xs sm:text-sm text-green-600">{stats.percentualAcerto.toFixed(1)}% de acerto</p>
                </div>
                <div className="bg-green-100 p-2 sm:p-3 rounded-full flex-shrink-0">
                  <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Investido</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900">{formatCurrency(stats.totalInvestido)}</p>
                </div>
                <div className="bg-orange-100 p-2 sm:p-3 rounded-full flex-shrink-0">
                  <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Lucro/Prejuízo</p>
                  <p className={`text-2xl sm:text-3xl font-bold ${stats.lucro >= 0 ? "text-green-600" : "text-red-600"}`}>
                    {formatCurrency(stats.lucro)}
                  </p>
                </div>
                <div className={`p-2 sm:p-3 rounded-full flex-shrink-0 ${stats.lucro >= 0 ? "bg-green-100" : "bg-red-100"}`}>
                  <TrendingUp className={`h-5 w-5 sm:h-6 sm:w-6 ${stats.lucro >= 0 ? "text-green-600" : "text-red-600"}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
          <Link href="/">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Trophy className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-semibold text-lg mb-2">Novos Bolões</h3>
                <p className="text-gray-600 text-sm">Participe de novos bolões disponíveis</p>
              </CardContent>
            </Card>
          </Link>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <History className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="font-semibold text-lg mb-2">Histórico</h3>
              <p className="text-gray-600 text-sm">Veja todas suas apostas anteriores</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Settings className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="font-semibold text-lg mb-2">Configurações</h3>
              <p className="text-gray-600 text-sm">Gerencie sua conta e preferências</p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="apostas" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="apostas">Minhas Apostas</TabsTrigger>
            <TabsTrigger value="historico">Histórico</TabsTrigger>
            <TabsTrigger value="perfil">Perfil</TabsTrigger>
          </TabsList>

          {/* Minhas Apostas Tab */}
          <TabsContent value="apostas">
            <Card>
              <CardHeader>
                <CardTitle>Minhas Apostas</CardTitle>
                <CardDescription>Acompanhe o status de todas suas apostas</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {minhasApostas.length === 0 ? (
                    <div className="text-center py-8">
                      <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">Você ainda não fez nenhuma aposta</p>
                      <Link href="/">
                        <Button className="mt-4 bg-green-600 hover:bg-green-700">Fazer Primeira Aposta</Button>
                      </Link>
                    </div>
                  ) : (
                    minhasApostas.map((aposta) => (
                      <div key={aposta.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="font-semibold text-lg">{aposta.bolao}</h3>
                            {getStatusBadge(aposta.status)}
                          </div>
                          <div className="flex items-center space-x-6 text-sm text-gray-600">
                            <span className="flex items-center">
                              <Target className="h-4 w-4 mr-1" />
                              {aposta.jogos} jogos
                            </span>
                            <span className="flex items-center">
                              <DollarSign className="h-4 w-4 mr-1" />
                              {formatCurrency(aposta.valor)}
                            </span>
                            <span className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              {formatDate(aposta.data)}
                            </span>
                            {aposta.premio && (
                              <span className="flex items-center text-green-600 font-medium">
                                <Trophy className="h-4 w-4 mr-1" />
                                Prêmio: {formatCurrency(aposta.premio)}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            Ver Detalhes
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Histórico Tab */}
          <TabsContent value="historico">
            <Card>
              <CardHeader>
                <CardTitle>Histórico de Transações</CardTitle>
                <CardDescription>Todas suas movimentações financeiras</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {historico.map((transacao) => (
                    <div key={transacao.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-1">
                          <h4 className="font-medium">{transacao.descricao}</h4>
                          <Badge
                            variant={
                              transacao.status === "concluido"
                                ? "default"
                                : transacao.status === "pendente"
                                  ? "secondary"
                                  : "destructive"
                            }
                          >
                            {transacao.status === "concluido"
                              ? "Concluído"
                              : transacao.status === "pendente"
                                ? "Pendente"
                                : "Cancelado"}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span className="capitalize">{transacao.tipo}</span>
                          <span>{formatDate(transacao.data)}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <span
                          className={`font-bold text-lg ${transacao.valor > 0 ? "text-green-600" : "text-red-600"}`}
                        >
                          {transacao.valor > 0 ? "+" : ""}
                          {formatCurrency(transacao.valor)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Perfil Tab */}
          <TabsContent value="perfil">
            <Card>
              <CardHeader>
                <CardTitle>Meu Perfil</CardTitle>
                <CardDescription>Gerencie suas informações pessoais</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="bg-green-100 p-4 rounded-full">
                      <User className="h-8 w-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold">{user?.nome}</h3>
                      <p className="text-gray-600">{user?.email}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Informações da Conta</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Tipo de Conta:</span>
                          <span className="capitalize">{user?.tipo}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <Badge variant="default">Ativo</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Membro desde:</span>
                          <span>Janeiro 2024</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-3">Estatísticas</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total de Apostas:</span>
                          <span>{stats.totalApostas}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Taxa de Acerto:</span>
                          <span>{stats.percentualAcerto.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Maior Prêmio:</span>
                          <span className="text-green-600 font-medium">R$ 120,00</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <Button variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      Editar Perfil
                    </Button>
                    <Button variant="outline">
                      <Lock className="h-4 w-4 mr-2" />
                      Alterar Senha
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
    </div>
  )
}
