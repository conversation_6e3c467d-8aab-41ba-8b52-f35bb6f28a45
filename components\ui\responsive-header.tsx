"use client"

import Link from "next/link"
import { Trophy } from "lucide-react"
import { cn } from "@/lib/utils"

interface ResponsiveHeaderProps {
  title?: string
  subtitle?: string
  href?: string
  className?: string
  variant?: "default" | "dark" | "light"
  sticky?: boolean
  children?: React.ReactNode
}

export function ResponsiveHeader({ 
  title = "Sistema Bolão",
  subtitle,
  href = "/",
  className,
  variant = "default",
  sticky = false,
  children
}: ResponsiveHeaderProps) {
  const baseClasses = "border-b shadow-sm"
  
  const variantClasses = {
    default: "bg-slate-800 border-slate-700 text-white",
    dark: "bg-slate-900 border-slate-800 text-white", 
    light: "bg-white border-gray-200 text-gray-900"
  }

  const titleColorClasses = {
    default: "text-green-400",
    dark: "text-green-400",
    light: "text-green-600"
  }

  return (
    <header className={cn(
      baseClasses,
      variantClasses[variant],
      sticky && "sticky top-0 z-50",
      className
    )}>
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo/Title - Centralizado em mobile */}
          <div className="flex-1 flex justify-center sm:justify-start">
            <Link 
              href={href} 
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity group"
            >
              <Trophy className={cn(
                "h-6 w-6 sm:h-8 sm:w-8 transition-transform group-hover:scale-110",
                titleColorClasses[variant]
              )} />
              <div className="text-center sm:text-left">
                <h1 className={cn(
                  "text-lg sm:text-2xl md:text-3xl font-bold",
                  titleColorClasses[variant]
                )}>
                  {title}
                </h1>
                {subtitle && (
                  <p className={cn(
                    "text-xs sm:text-sm opacity-75",
                    variant === "light" ? "text-gray-600" : "text-gray-300"
                  )}>
                    {subtitle}
                  </p>
                )}
              </div>
            </Link>
          </div>
          
          {/* Actions - Posicionado à direita */}
          {children && (
            <div className="flex items-center gap-2 sm:gap-4 absolute right-3 sm:relative sm:right-0">
              {children}
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

// Componente específico para páginas de auth
export function AuthHeader({ 
  title = "Sistema Bolão",
  subtitle,
  href = "/",
  className 
}: Pick<ResponsiveHeaderProps, "title" | "subtitle" | "href" | "className">) {
  return (
    <div className={cn("text-center mb-6 sm:mb-8", className)}>
      <Link
        href={href}
        className="inline-flex items-center space-x-2 text-xl sm:text-2xl font-bold text-gray-900 hover:text-green-600 transition-colors group"
      >
        <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 transition-transform group-hover:scale-110" />
        <span>{title}</span>
      </Link>
      {subtitle && (
        <p className="text-gray-600 mt-2 text-sm sm:text-base">{subtitle}</p>
      )}
    </div>
  )
}

// Componente para headers de páginas internas
export function PageHeader({
  title,
  subtitle,
  children,
  className
}: {
  title: string
  subtitle?: string
  children?: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn("mb-6 sm:mb-8", className)}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="text-center sm:text-left">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 mt-2 text-sm sm:text-base">{subtitle}</p>
          )}
        </div>
        {children && (
          <div className="flex justify-center sm:justify-end">
            {children}
          </div>
        )}
      </div>
    </div>
  )
}
