import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const userId = searchParams.get('userId') || '1' // Por enquanto usar usuário padrão

    // Buscar pagamentos do usuário
    const pagamentos = await executeQuery(`
      SELECT 
        p.id,
        p.valor,
        p.tipo,
        p.status,
        p.metodo_pagamento as metodo,
        p.descricao,
        p.data_pagamento as data,
        p.referencia_externa as referencia,
        p.transaction_id
      FROM pagamentos p
      WHERE p.usuario_id = ?
      ORDER BY p.data_pagamento DESC
      LIMIT 100
    `, [userId])

    // Se não houver pagamentos, buscar dados dos bilhetes para simular
    if (pagamentos.length === 0) {
      const bilhetes = await executeQuery(`
        SELECT 
          b.id,
          b.codigo_bilhete as codigo,
          b.valor_total as valor,
          b.status,
          b.data_aposta as data,
          b.transaction_id,
          b.pix_order_id,
          bo.nome as bolao_nome
        FROM apostas b
        LEFT JOIN boloes bo ON b.bolao_id = bo.id
        WHERE b.usuario_id = ?
        ORDER BY b.data_aposta DESC
        LIMIT 50
      `, [userId])

      // Converter bilhetes em formato de pagamentos
      const pagamentosFromBilhetes = bilhetes.map((bilhete: any) => ({
        id: `bil_${bilhete.id}`,
        valor: parseFloat(bilhete.valor) || 0,
        tipo: 'saida',
        status: mapBilheteStatusToPagamento(bilhete.status),
        metodo: 'PIX',
        descricao: `Aposta - ${bilhete.bolao_nome || 'Bolão'} (${bilhete.codigo})`,
        data: bilhete.data,
        referencia: bilhete.codigo,
        transaction_id: bilhete.transaction_id
      }))

      return NextResponse.json(pagamentosFromBilhetes)
    }

    // Formatar dados para o frontend
    const pagamentosFormatted = pagamentos.map((pagamento: any) => ({
      id: pagamento.id,
      valor: parseFloat(pagamento.valor) || 0,
      tipo: pagamento.tipo || 'saida',
      status: pagamento.status || 'pendente',
      metodo: pagamento.metodo || 'PIX',
      descricao: pagamento.descricao || 'Pagamento',
      data: pagamento.data,
      referencia: pagamento.referencia,
      transaction_id: pagamento.transaction_id
    }))

    return NextResponse.json(pagamentosFormatted)

  } catch (error) {
    console.error('❌ Erro ao buscar pagamentos:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      usuario_id = 1, 
      valor, 
      tipo, 
      metodo_pagamento, 
      descricao,
      referencia_externa 
    } = body

    if (!valor || !tipo || !metodo_pagamento) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Inserir pagamento
    const result = await executeQuery(`
      INSERT INTO pagamentos (
        usuario_id, 
        valor, 
        tipo, 
        status, 
        metodo_pagamento, 
        descricao,
        referencia_externa,
        data_pagamento
      ) VALUES (?, ?, ?, 'pendente', ?, ?, ?, NOW())
    `, [usuario_id, valor, tipo, metodo_pagamento, descricao, referencia_externa])

    const pagamento_id = (result as any).insertId

    return NextResponse.json({
      success: true,
      pagamento_id,
      message: 'Pagamento registrado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar pagamento:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function mapBilheteStatusToPagamento(status: string) {
  switch (status) {
    case 'pendente':
      return 'pendente'
    case 'pago':
    case 'paga':
      return 'aprovado'
    case 'cancelado':
    case 'cancelada':
      return 'cancelado'
    default:
      return 'pendente'
  }
}
