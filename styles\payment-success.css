/* CENTRALIZAÇÃO FORÇADA PARA MODAL DE PAGAMENTO */

/* Overlay com centralização */
[data-radix-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 50 !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(4px) !important;
}

/* Conteúdo do modal centralizado */
[data-radix-dialog-content] {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 51 !important;
  margin: 0 !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  background-color: #1E293B !important; /* Fundo azul-acinzentado escuro */
  color: white !important; /* Texto branco */
}

/* Classe específica para o modal de sucesso */
.payment-success-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  margin: 0 !important;
}

/* Remover animações que interferem na centralização */

@keyframes paymentSuccessSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Animação para os confetes */
.confetti-animation {
  animation: confettiFall 2s ease-in-out infinite;
}

@keyframes confettiFall {
  0% {
    transform: translateY(-10px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(20px) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(50px) rotate(360deg);
    opacity: 0;
  }
}

/* Efeito de brilho no ícone de sucesso */
.success-icon {
  animation: successGlow 2s ease-in-out infinite alternate;
}

@keyframes successGlow {
  from {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.6);
  }
}

/* Efeito de pulso no título */
.success-title {
  animation: successPulse 1.5s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Responsividade para telas muito pequenas */
@media (max-width: 320px) {
  .payment-success-modal,
  [data-radix-dialog-content] {
    width: 98vw !important;
    max-width: none !important;
    margin: 0.25rem !important;
    padding: 0.75rem !important;
    background-color: #1E293B !important;
    color: white !important;
  }

  .success-icon {
    width: 3rem !important;
    height: 3rem !important;
  }

  .success-title {
    font-size: 1.25rem !important;
    color: white !important;
  }
}

/* Melhorias para tablets */
@media (min-width: 768px) and (max-width: 1024px) {
  .payment-success-modal {
    max-width: 28rem !important;
  }
}

/* Garantir que o backdrop seja visível */
.dialog-overlay {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px) !important;
}

/* Efeito hover nos botões */
.success-button {
  transition: all 0.2s ease-in-out;
}

.success-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animação para o status badge */
.status-badge {
  animation: statusBadgeGlow 2s ease-in-out infinite alternate;
}

@keyframes statusBadgeGlow {
  from {
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.8);
  }
}

/* Melhorar a legibilidade em telas pequenas */
@media (max-width: 480px) {
  .payment-info-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.25rem !important;
  }
  
  .payment-info-label {
    font-size: 0.875rem !important;
  }
  
  .payment-info-value {
    font-size: 1rem !important;
    word-break: break-all;
  }
}

/* Garantir que o modal não ultrapasse a altura da tela */
.payment-success-content {
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* Scrollbar customizada para o modal */
.payment-success-content::-webkit-scrollbar {
  width: 6px;
}

.payment-success-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.payment-success-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.payment-success-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Estilos específicos para iOS */
@supports (-webkit-touch-callout: none) {
  [data-radix-dialog-content] {
    -webkit-overflow-scrolling: touch !important;
    background-color: #1E293B !important;
    color: white !important;
  }
}

/* Estilos específicos para Android */
@media screen and (max-device-width: 640px) and (orientation: portrait) {
  [data-radix-dialog-content] {
    max-height: 95vh !important;
    background-color: #1E293B !important;
    color: white !important;
  }
}

/* Estilos para dispositivos com notch (iPhone X+) */
@media screen and (max-width: 640px) and (orientation: portrait) and (min-height: 812px) {
  [data-radix-dialog-content] {
    max-height: 90vh !important;
    margin-top: env(safe-area-inset-top, 0) !important;
    margin-bottom: env(safe-area-inset-bottom, 0) !important;
  }
}

/* Estilos para landscape em dispositivos móveis */
@media screen and (max-height: 640px) and (orientation: landscape) {
  [data-radix-dialog-content] {
    max-height: 85vh !important;
    width: 90vw !important;
    max-width: 600px !important;
  }
}
