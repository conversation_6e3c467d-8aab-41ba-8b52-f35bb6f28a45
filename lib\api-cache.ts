// Cache simples em memória para APIs
interface CacheItem {
  data: any
  timestamp: number
  ttl: number
}

class ApiCache {
  private cache: Map<string, CacheItem> = new Map()

  set(key: string, data: any, ttlMinutes: number = 5): void {
    const ttl = ttlMinutes * 60 * 1000 // Converter para millisegundos
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get(key: string): any | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // Verificar se o cache expirou
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Limpar cache expirado
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Obter estatísticas do cache
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Instância singleton do cache
export const apiCache = new ApiCache()

// Executar limpeza do cache a cada 10 minutos
if (typeof window === 'undefined') { // Apenas no servidor
  setInterval(() => {
    apiCache.cleanup()
  }, 10 * 60 * 1000)
}

// Helper para criar chaves de cache
export const createCacheKey = (prefix: string, params: Record<string, any> = {}): string => {
  const paramString = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|')
  
  return paramString ? `${prefix}:${paramString}` : prefix
}

// Decorator para cache de funções
export const withCache = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyPrefix: string,
  ttlMinutes: number = 5
): T => {
  return (async (...args: any[]) => {
    const cacheKey = createCacheKey(keyPrefix, { args: JSON.stringify(args) })
    
    // Tentar buscar do cache
    const cached = apiCache.get(cacheKey)
    if (cached !== null) {
      console.log(`🎯 Cache hit: ${cacheKey}`)
      return cached
    }

    // Executar função e cachear resultado
    console.log(`🔄 Cache miss: ${cacheKey}`)
    const result = await fn(...args)
    apiCache.set(cacheKey, result, ttlMinutes)
    
    return result
  }) as T
}

// Middleware para invalidar cache baseado em padrões
export const invalidateCache = (pattern: string): void => {
  const keys = Array.from(apiCache['cache'].keys())
  const keysToDelete = keys.filter(key => key.includes(pattern))
  
  keysToDelete.forEach(key => {
    apiCache.delete(key)
    console.log(`🗑️ Cache invalidated: ${key}`)
  })
}

// Cache específico para dados do usuário
export const userCache = {
  get: (userId: string | number) => apiCache.get(`user:${userId}`),
  set: (userId: string | number, data: any, ttlMinutes: number = 15) => 
    apiCache.set(`user:${userId}`, data, ttlMinutes),
  delete: (userId: string | number) => apiCache.delete(`user:${userId}`),
  invalidateAll: () => invalidateCache('user:')
}

// Cache específico para dados de apostas
export const betsCache = {
  get: (userId: string | number) => apiCache.get(`bets:${userId}`),
  set: (userId: string | number, data: any, ttlMinutes: number = 5) => 
    apiCache.set(`bets:${userId}`, data, ttlMinutes),
  delete: (userId: string | number) => apiCache.delete(`bets:${userId}`),
  invalidateAll: () => invalidateCache('bets:')
}

// Cache específico para dados de pagamentos
export const paymentsCache = {
  get: (userId: string | number) => apiCache.get(`payments:${userId}`),
  set: (userId: string | number, data: any, ttlMinutes: number = 10) => 
    apiCache.set(`payments:${userId}`, data, ttlMinutes),
  delete: (userId: string | number) => apiCache.delete(`payments:${userId}`),
  invalidateAll: () => invalidateCache('payments:')
}

// Cache específico para dados de afiliados
export const affiliateCache = {
  get: (userId: string | number) => apiCache.get(`affiliate:${userId}`),
  set: (userId: string | number, data: any, ttlMinutes: number = 30) => 
    apiCache.set(`affiliate:${userId}`, data, ttlMinutes),
  delete: (userId: string | number) => apiCache.delete(`affiliate:${userId}`),
  invalidateAll: () => invalidateCache('affiliate:')
}
